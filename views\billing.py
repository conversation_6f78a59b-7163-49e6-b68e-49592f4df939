import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
from datetime import datetime
from tkcalendar import Calendar
import logging
from PIL import Image, ImageTk
from resources import resource_path
import re
from .PaymentReceipt import PaymentReceipt
from login import current_user
from datetime import date
from calendar import monthrange
from database import Database

class Tooltip:
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip = None
        self.widget.bind("<Enter>", self.enter)
        self.widget.bind("<Leave>", self.leave)
        self.widget.bind("<ButtonPress>", self.leave)

    def enter(self, event=None):
        self.tooltip = tk.Toplevel(self.widget)
        self.tooltip.wm_overrideredirect(True)
        self.tooltip.wm_attributes("-topmost", True)
        
        label = tk.Label(
            self.tooltip,
            text=self.text,
            background="#4A6FA5",
            relief="solid",
            borderwidth=1,
            font=("Helvetica", 10),
            fg="#FFFFFF"
        )
        label.pack()
        
        widget_x = self.widget.winfo_rootx()
        widget_y = self.widget.winfo_rooty()
        widget_width = self.widget.winfo_width()
        widget_height = self.widget.winfo_height()
        
        self.tooltip.update_idletasks()
        tooltip_width = self.tooltip.winfo_width()
        tooltip_height = self.tooltip.winfo_height()
        
        screen_width = self.widget.winfo_screenwidth()
        screen_height = self.widget.winfo_screenheight()
        
        x = widget_x + (widget_width - tooltip_width) // 2
        y = widget_y + widget_height + 5
        
        if x + tooltip_width > screen_width:
            x = screen_width - tooltip_width - 5
        elif x < 0:
            x = 5
            
        if y + tooltip_height > screen_height:
            y = widget_y - tooltip_height - 5
            if y < 0:
                y = 5
        
        self.tooltip.wm_geometry(f"+{int(x)}+{int(y)}")

    def leave(self, event=None):
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None

class BillingManager(tk.Frame):
    COLORS = {
        'background': '#F8F9FA',
        'card_bg': '#FFFFFF',
        'primary_accent': '#4A6FA5',
        'secondary_accent': '#6C8FC7',
        'text_primary': '#2D3748',
        'text_secondary': '#718096',
        'button_start': '#4A6FA5',
        'button_end': '#3A5A8C',
        'transparent': 'transparent',
        'warning': '#E53E3E',
        'input_bg': '#EDF2F7',
        'border': '#E2E8F0'
    }

    def __init__(self, parent, nav_commands, customer_id=None):
        super().__init__(parent)
        self.nav_commands = nav_commands
        self.customer_id = customer_id
        self.current_date = datetime.now()
        self.current_year = self.current_date.year
        self.current_month = self.current_date.month
        self.current_day = self.current_date.day
        self.payment_receipt = PaymentReceipt(self)
        self.specific_customer_view = customer_id is not None
        self.db = Database()
        self.config(bg=self.COLORS['background'])
        self.pack(fill="both", expand=True)

        try:
            self._setup_ui()
            self._load_regions()
            self._load_customers()
            self._load_bills()
            self._ensure_monthly_bills()
        except Exception as e:
            logging.error(f"Initialization error: {str(e)}")
            messagebox.showerror("Error", f"Failed to initialize: {str(e)}")
            if hasattr(self, 'db'):
                self.db.close_connection()

    def __del__(self):
        if hasattr(self, 'db'):
            self.db.close_connection()

    def _setup_ui(self):
        try:
            header = tk.Frame(self, bg=self.COLORS['primary_accent'], height=80)
            header.pack(fill="x", pady=(0, 20))

            def load_and_process_image(relative_path):
                try:
                    full_path = resource_path(relative_path)
                    if os.path.exists(full_path):
                        img = Image.open(full_path).convert("RGBA")
                        data = img.getdata()
                        new_data = []
                        for item in data:
                            if item[3] > 0:
                                new_data.append((255, 255, 255, item[3]))
                            else:
                                new_data.append(item)
                        img.putdata(new_data)
                        img = img.resize((30, 30), Image.Resampling.LANCZOS)
                        return ImageTk.PhotoImage(img)
                    return ImageTk.PhotoImage(Image.new('RGBA', (30, 30), (255, 255, 255, 0)))
                except Exception as e:
                    print(f"Error loading image: {str(e)}")
                    return ImageTk.PhotoImage(Image.new('RGBA', (30, 30), (255, 255, 255, 0)))

            self.dashboard_icon = load_and_process_image('assets/dashboard/billing/dashboard.png')
            self.customers_icon = load_and_process_image('assets/dashboard/billing/left-arrow.png')
            self.add_bill_icon = load_and_process_image('assets/dashboard/billing/addmanualbill.png')
            self.mark_paid_icon = load_and_process_image('assets/dashboard/billing/markpaid.png')
            self.delete_bill_icon = load_and_process_image('assets/dashboard/billing/deletebill.png')

            dashboard_btn = tk.Button(header, image=self.dashboard_icon, 
                                     command=self.nav_commands['show_dashboard'],
                                     bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                     activebackground=self.COLORS['secondary_accent'])
            dashboard_btn.pack(side="left", padx=10)
            Tooltip(dashboard_btn, "Dashboard")

            header_label = tk.Label(header, text="Billing Management", 
                                  font=("Helvetica", 24, "bold"),
                                  fg="#FFFFFF", bg=self.COLORS['primary_accent'])
            header_label.place(relx=0.5, rely=0.5, anchor="center")

            button_frame = tk.Frame(header, bg=self.COLORS['primary_accent'])
            button_frame.pack(side="right", padx=10)

            customers_btn = tk.Button(button_frame, image=self.customers_icon, 
                                    command=self.nav_commands['show_customers'],
                                    bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                    activebackground=self.COLORS['secondary_accent'])
            customers_btn.pack(side="left", padx=5)
            Tooltip(customers_btn, "Customers")

            add_bill_btn = tk.Button(button_frame, image=self.add_bill_icon, 
                                    command=self._add_manual_bill,
                                    bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                    activebackground=self.COLORS['secondary_accent'])
            add_bill_btn.pack(side="left", padx=5)
            Tooltip(add_bill_btn, "Add Manual Bill")

            self.mark_paid_btn = tk.Button(button_frame, image=self.mark_paid_icon, 
                                        command=self._mark_paid,
                                        bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                        activebackground=self.COLORS['secondary_accent'])
            self.mark_paid_btn.pack(side="left", padx=5)
            Tooltip(self.mark_paid_btn, "Mark Paid")

            delete_bill_btn = tk.Button(button_frame, image=self.delete_bill_icon, 
                                       command=self._delete_bill,
                                       bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                       activebackground=self.COLORS['secondary_accent'])
            delete_bill_btn.pack(side="left", padx=5)
            Tooltip(delete_bill_btn, "Delete Bill")

            content = tk.Frame(self, bg=self.COLORS['card_bg'])
            content.pack(fill="both", expand=True, padx=5, pady=5)

            filter_frame = tk.Frame(content, bg=self.COLORS['card_bg'])
            filter_frame.pack(fill="x", pady=(0, 5))

            tk.Label(filter_frame, text="Filter by Region:", bg=self.COLORS['card_bg'],
                     fg=self.COLORS['text_secondary'], font=("Helvetica", 10)).pack(side="left", padx=5)

            self.region_filter_var = tk.StringVar(value="All")
            self.region_filter_dropdown = ttk.Combobox(filter_frame, textvariable=self.region_filter_var,
                                                      state="readonly", font=("Helvetica", 12), width=20)
            self.region_filter_dropdown.pack(side="left", padx=5)
            self.region_filter_dropdown.bind("<<ComboboxSelected>>", lambda e: self._load_bills())

            tk.Label(filter_frame, text="Search Customer:", font=("Helvetica", 10),
                     bg=self.COLORS['card_bg'], fg=self.COLORS['text_secondary']).pack(side="left", padx=(10, 5))
            
            self.customer_search_var = tk.StringVar()
            if not self.specific_customer_view:
                self.customer_search_var.trace('w', self._on_search_change)
            
            self.customer_combobox = ttk.Combobox(filter_frame, textvariable=self.customer_search_var,
                                                font=("Helvetica", 12), width=20)
            self.customer_combobox.pack(side="left", padx=5)
            
            if not self.specific_customer_view:
                self.customer_combobox.bind("<<ComboboxSelected>>", lambda e: self._on_customer_selected())
            else:
                self.customer_combobox.config(state='disabled')

            tk.Label(filter_frame, text="Search by Paid Date:", font=("Helvetica", 10),
                     bg=self.COLORS['card_bg'], fg=self.COLORS['text_secondary']).pack(side="left", padx=(10, 5))
            
            self.paid_date_var = tk.StringVar()
            self.paid_date_entry = tk.Entry(filter_frame, textvariable=self.paid_date_var,
                                            font=("Helvetica", 12), width=20, state='readonly')
            self.paid_date_entry.pack(side="left", padx=5)
            self.paid_date_entry.bind("<Button-1>", self._show_calendar)
            self.paid_date_var.trace('w', lambda *args: self._load_bills())

            tk.Label(filter_frame, text="Filter:", font=("Helvetica", 10),
                     bg=self.COLORS['card_bg'], fg=self.COLORS['text_secondary']).pack(side="left", padx=(10, 0))
            self.filter_var = tk.StringVar(value="all")
            ttk.Radiobutton(filter_frame, text="All", variable=self.filter_var, value="all",
                            command=self._load_bills).pack(side="left", padx=5)
            ttk.Radiobutton(filter_frame, text="Paid", variable=self.filter_var, value="paid",
                            command=self._load_bills).pack(side="left", padx=5)
            ttk.Radiobutton(filter_frame, text="Unpaid", variable=self.filter_var, value="unpaid",
                            command=self._load_bills).pack(side="left", padx=5)

            tree_frame = tk.Frame(content, bg=self.COLORS['card_bg'])
            tree_frame.pack(fill="both", expand=True, pady=(0, 5))

            style = ttk.Style()
            style.configure("Treeview",
                            background=self.COLORS['card_bg'],
                            foreground=self.COLORS['text_secondary'],
                            fieldbackground=self.COLORS['card_bg'],
                            font=("Helvetica", 10),
                            rowheight=25,
                            borderwidth=1,
                            relief="solid",
                            bordercolor=self.COLORS['border'])
            style.configure("Treeview.Heading",
                            font=("Helvetica", 10, "bold"),
                            foreground=self.COLORS['text_secondary'],
                            background=self.COLORS['input_bg'],
                            relief="flat",
                            padding=5)
            style.map("Treeview",
                      background=[('selected', self.COLORS['primary_accent'])],
                      foreground=[('selected', '#FFFFFF')])

            scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL)
            scrollbar.pack(side="right", fill="y")

            self.tree = ttk.Treeview(
                tree_frame, 
                columns=("id", "invoice", "customer", "region", "package", "month", 
                         "amount", "paid_amount", "status", "paid_date", "customer_status", 
                         "paid_by", "balance", "credit"),
                selectmode="browse", 
                show="headings", 
                yscrollcommand=scrollbar.set,
                style="Treeview"
            )
            scrollbar.config(command=self.tree.yview)

            columns = [
                ("id", "Bill ID", 40, "center"),
                ("invoice", "Invoice #", 100, "center"),
                ("customer", "Customer", 120, "w"),
                ("region", "Region", 100, "center"),
                ("package", "Package", 120, "center"),
                ("month", "Month", 75, "center"),
                ("amount", "Month Bill", 75, "center"),
                ("paid_amount", "Paid Amount", 110, "center"),
                ("status", "Status", 100, "center"),
                ("paid_date", "Paid Date", 100, "center"),
                ("customer_status", "Suspend", 70, "center"),
                ("paid_by", "Paid By", 110, "center"),
                ("balance", "Outstanding", 90, "center"),
                ("credit", "Credit", 100, "center")
            ]

            for col_id, heading, width, anchor in columns:
                self.tree.heading(col_id, text=heading, anchor=anchor)
                self.tree.column(col_id, width=width, anchor=anchor)

            self.tree.pack(fill="both", expand=True)

        except Exception as e:
            logging.error(f"Error in UI setup: {str(e)}")
            messagebox.showerror("Error", f"Failed to set up UI: {str(e)}")

    def _show_calendar(self, event):
        def on_date_select():
            selected_date = cal.get_date()
            try:
                parsed_date = datetime.strptime(selected_date, '%m/%d/%y').strftime('%Y-%m-%d')
                self.paid_date_var.set(parsed_date)
            except ValueError:
                messagebox.showerror("Error", "Invalid date format selected")
            top.destroy()

        top = tk.Toplevel(self)
        top.transient(self)
        top.grab_set()
        top.title("Select Paid Date")
        top.geometry("300x300")
        
        top.update_idletasks()
        width = top.winfo_width()
        height = top.winfo_height()
        x = (top.winfo_screenwidth() // 2) - (width // 2)
        y = (top.winfo_screenheight() // 2) - (height // 2)
        top.geometry(f"{width}x{height}+{x}+{y}")

        cal = Calendar(top, selectmode='day', date_pattern='mm/dd/yy')
        cal.pack(padx=10, pady=10, fill="both", expand=True)

        tk.Button(top, text="Select", command=on_date_select, bg=self.COLORS['primary_accent'], fg="#FFFFFF",
                  font=("Helvetica", 12, "bold"), relief=tk.FLAT, padx=10, pady=5,
                  activebackground=self.COLORS['secondary_accent'], activeforeground="#FFFFFF").pack(pady=5)

    def _validate_input(self, char, current_text):
        if len(current_text) >= 25:
            return False
        return bool(re.match(r'^[a-zA-Z0-9\s]*$', char))

    def _update_billing_table_schema(self):
        try:
            db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            conn = sqlite3.connect(db_path)
            c = conn.cursor()

            c.execute("PRAGMA table_info(billing)")
            columns = [col[1] for col in c.fetchall()]
            
            if 'status' not in columns:
                c.execute("ALTER TABLE billing ADD COLUMN status TEXT DEFAULT 'Unpaid'")

            if 'invoice_number' not in columns or 'paid_by' not in columns or 'paid_amount' not in columns:
                c.execute('''CREATE TABLE billing_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_id INTEGER,
                    month INTEGER,
                    year INTEGER,
                    amount REAL NOT NULL,
                    paid_amount REAL DEFAULT 0,
                    is_paid INTEGER DEFAULT 0,
                    paid_date TEXT,
                    invoice_number TEXT,
                    paid_by TEXT,
                    status TEXT DEFAULT 'Unpaid',
                    FOREIGN KEY (customer_id) REFERENCES customers(id)
                )''')
                c.execute('''INSERT INTO billing_new (id, customer_id, month, year, amount, is_paid, paid_date, invoice_number, paid_by, status)
                             SELECT id, customer_id, month, year, amount, is_paid, paid_date, invoice_number, paid_by, 
                             CASE WHEN is_paid = 1 THEN 'Paid' ELSE 'Unpaid' END as status
                             FROM billing''')
                c.execute("DROP TABLE billing")
                c.execute("ALTER TABLE billing_new RENAME TO billing")
                conn.commit()
            conn.close()
        except Exception as e:
            logging.error(f"Error updating billing table schema: {str(e)}")
            raise

    def _load_regions(self):
        try:
            db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
            conn = sqlite3.connect(db_path)
            c = conn.cursor()
            c.execute("SELECT name FROM regions ORDER BY name")
            self.regions = [row[0] for row in c.fetchall()]
            conn.close()
            self.region_filter_dropdown['values'] = ['All'] + self.regions
            if not self.region_filter_var.get():
                self.region_filter_var.set('All')
        except Exception as e:
            logging.error(f"Error loading regions: {str(e)}")
            messagebox.showerror("Error", f"Failed to load regions: {str(e)}")
            self.regions = []

    def _on_search_change(self, *args):
        search_term = self.customer_search_var.get().lower()
        if not hasattr(self, 'customer_names'):
            return

        if not search_term:
            self.customer_combobox['values'] = self.customer_names
            self._load_bills()
        else:
            filtered = [name for name in self.customer_names if name.lower().startswith(search_term)]
            self.customer_combobox['values'] = filtered

    def _on_customer_selected(self):
        self._load_bills()

    def _load_customers(self):
        try:
            db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
            conn = sqlite3.connect(db_path)
            c = conn.cursor()
            c.execute("SELECT id, name FROM customers")
            self.customers = {name: id for id, name in c.fetchall()}
            self.customer_names = sorted(self.customers.keys())
            self.customer_combobox['values'] = self.customer_names
            
            if self.customer_id is not None:
                c.execute("SELECT name FROM customers WHERE id = ?", (self.customer_id,))
                customer_name = c.fetchone()
                if customer_name:
                    self.customer_search_var.set(customer_name[0])
            
            conn.close()
        except Exception as e:
            logging.error(f"Error loading customers: {str(e)}")
            messagebox.showerror("Error", f"Failed to load customers: {str(e)}")
            self.customers = {}
            self.customer_names = []

    def _load_bills(self):
        try:
            db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
            conn = sqlite3.connect(db_path)
            c = conn.cursor()

            for item in self.tree.get_children():
                self.tree.delete(item)

            query = '''
                SELECT b.id, b.invoice_number, c.name, c.region, b.month, b.year, b.amount, 
                       b.paid_amount, b.status, b.paid_date, p.name as package_name, 
                       c.id as customer_id, c.status, b.paid_by, b.is_manual,
                       b.outstanding_amount, b.credit_amount
                FROM billing b
                JOIN customers c ON b.customer_id = c.id
                LEFT JOIN packages p ON c.package_id = p.id
                WHERE 1=1
            '''

            conditions = []
            params = []

            if self.customer_id is not None:
                conditions.append("c.id = ?")
                params.append(self.customer_id)
            else:
                selected_region = self.region_filter_var.get()
                if selected_region != "All":
                    conditions.append("c.region = ?")
                    params.append(selected_region)
                    
                customer_name = self.customer_search_var.get()
                if customer_name and customer_name in self.customers:
                    conditions.append("c.id = ?")
                    params.append(self.customers[customer_name])

            if self.filter_var.get() == "paid":
                conditions.append("b.status = 'Paid'")
            elif self.filter_var.get() == "unpaid":
                conditions.append("b.status != 'Paid'")
                
            selected_paid_date = self.paid_date_var.get()
            if selected_paid_date:
                conditions.append("b.paid_date = ?")
                params.append(selected_paid_date)

            if conditions:
                query += " AND " + " AND ".join(conditions)
            query += " ORDER BY b.year DESC, b.month DESC, b.id DESC"

            c.execute(query, params)
            bills = c.fetchall()

            print(f"[DEBUG] Loaded {len(bills)} bills for customer_id={self.customer_id}")
            for bill in bills:
                print(f"[DEBUG] Bill: {bill}")

            outstanding_info = {}
            for bill in bills:
                customer_id = bill[11]
                if customer_id not in outstanding_info:
                    c.execute('''
                        SELECT SUM(b.amount - COALESCE(b.paid_amount, 0)) as total_outstanding
                        FROM billing b
                        WHERE b.customer_id = ? AND (b.amount - COALESCE(b.paid_amount, 0)) > 0
                    ''', (customer_id,))
                    total_outstanding = c.fetchone()[0] or 0.0
                    outstanding_info[customer_id] = total_outstanding

            credit_info = {}
            for bill in bills:
                customer_id = bill[11]
                if customer_id not in credit_info:
                    # Get customer's stored credit balance
                    c.execute('''
                        SELECT credit_balance
                        FROM customers
                        WHERE id = ?
                    ''', (customer_id,))
                    customer_credit = c.fetchone()
                    customer_credit_amount = customer_credit[0] if customer_credit and customer_credit[0] is not None else 0.0

                    # Also get the latest bill credit amount
                    c.execute('''
                        SELECT credit_amount FROM billing
                        WHERE customer_id = ? AND status = 'Paid' AND credit_amount > 0
                        ORDER BY year DESC, month DESC, id DESC
                        LIMIT 1
                    ''', (customer_id,))
                    bill_credit = c.fetchone()
                    bill_credit_amount = bill_credit[0] if bill_credit and bill_credit[0] is not None else 0.0

                    # Use the maximum of both to ensure consistency
                    credit_info[customer_id] = max(customer_credit_amount, bill_credit_amount)

            for bill in bills:
                bill_id = str(bill[0]).zfill(4)
                invoice_number = bill[1] if bill[1] else ""
                customer_name = bill[2]
                region = bill[3] if bill[3] else "N/A"
                package_name = bill[10]
                customer_id = bill[11]
                customer_status = 'Active' if bill[12] == 1 else 'Inactive'
                bill_month = bill[4]
                bill_year = bill[5]
                paid_by = bill[13] if bill[13] else ""
                paid_amount = bill[7] if bill[7] else 0.0
                manual_amount = bill[6] if bill[6] else 0.0
                is_manual = bill[14] if len(bill) > 14 else 0
                outstanding_amount = bill[15] if len(bill) > 15 else 0.0
                credit_amount = bill[16] if len(bill) > 16 else 0.0

                c.execute('''SELECT p.price FROM customers c JOIN packages p ON c.package_id = p.id WHERE c.id = ?''', (customer_id,))
                package_price = c.fetchone()[0]

                c.execute('''SELECT pr.name, pr.price 
                             FROM customer_purchases cp
                             JOIN products pr ON cp.product_id = pr.id
                             WHERE cp.billing_id = ?''', (bill[0],))
                purchased_products = c.fetchall()
                product_total = sum(price for _, price in purchased_products)

                if manual_amount > 0:
                    base_amount = manual_amount
                else:
                    base_amount = package_price

                if product_total > 0:
                    base_amount += product_total

                outstanding_display = ""
                # Show customer's total outstanding amount on their latest unpaid bill
                if customer_id in outstanding_info and outstanding_info[customer_id] > 0:
                    c.execute('''
                        SELECT id FROM billing
                        WHERE customer_id = ? AND status != 'Paid'
                        ORDER BY year DESC, month DESC
                        LIMIT 1
                    ''', (customer_id,))
                    latest_unpaid = c.fetchone()
                    if latest_unpaid and latest_unpaid[0] == bill[0]:
                        outstanding_display = f"{outstanding_info[customer_id]:.2f}"
                # Also check for imported outstanding amount stored in customer table
                elif outstanding_amount > 0:
                    c.execute('''
                        SELECT id FROM billing
                        WHERE customer_id = ? AND status != 'Paid'
                        ORDER BY year DESC, month DESC
                        LIMIT 1
                    ''', (customer_id,))
                    latest_unpaid = c.fetchone()
                    if latest_unpaid and latest_unpaid[0] == bill[0]:
                        outstanding_display = f"{outstanding_amount:.2f}"

                credit_display = ""
                if credit_amount > 0:
                    credit_display = f"{credit_amount:.2f}"

                if is_manual == 1:
                    display_package = "Manual Bill"
                elif purchased_products:
                    product_names = [name for name, _ in purchased_products]
                    display_package = ', '.join(product_names)
                else:
                    display_package = package_name

                status = bill[8] if bill[8] else "Unpaid"
                paid_date = bill[9] if bill[8] in('Paid', 'Partially Paid') and bill[9] else ""
                # Always insert the bill, even if paid_date is None
                self.tree.insert("", "end", values=(
                    bill_id, 
                    invoice_number, 
                    customer_name, 
                    region, 
                    display_package,
                    f"{bill[4]:02d}/{bill[5]}", 
                    f"{manual_amount:.2f}",
                    f"{paid_amount:.2f}" if paid_amount else "0.00",
                    status, 
                    paid_date, 
                    customer_status, 
                    paid_by,
                    outstanding_display,
                    credit_display
                ))

            conn.close()
        except Exception as e:
            logging.error(f"Error loading bills: {str(e)}")
            messagebox.showerror("Error", f"Failed to load bills: {str(e)}")

    def _ensure_monthly_bills(self):
        try:
            db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
            conn = sqlite3.connect(db_path)
            c = conn.cursor()

            # Include both active customers and inactive customers with outstanding/credit amounts
            c.execute('''SELECT c.id, p.price, c.credit_balance, c.outstanding_amount, c.status
                         FROM customers c 
                         LEFT JOIN packages p ON c.package_id = p.id
                         WHERE c.status = 1 OR (c.status = 0 AND (c.outstanding_amount > 0 OR c.credit_balance > 0))''')
            customers = c.fetchall()

            for customer_id, amount, credit_balance, outstanding_amount, status in customers:
                # Always skip if any bill exists for this customer/month/year (imported or system-generated)
                c.execute('''SELECT id FROM billing
                             WHERE customer_id = ? AND month = ? AND year = ?''',
                          (customer_id, self.current_month, self.current_year))
                if c.fetchone():
                    continue

                # Only create monthly bills for active customers on the 1st of the month
                if status == 1 and self.current_day == 1:
                    c.execute('''INSERT INTO billing (customer_id, month, year, amount, is_paid)
                                 VALUES (?, ?, ?, ?, ?)''',
                              (customer_id, self.current_month, self.current_year, amount, 0))
                # For inactive customers with outstanding/credit, only create billing records if none exist
                # This prevents duplicate billing records that could affect imported data
                elif status == 0 and (outstanding_amount > 0 or credit_balance > 0):
                    # Check if any billing record exists for this customer (imported or system-generated)
                    c.execute('''SELECT COUNT(*) FROM billing WHERE customer_id = ?''', (customer_id,))
                    existing_bills_count = c.fetchone()[0]

                    # Only create billing records if no bills exist at all for this customer
                    # This preserves imported billing data and prevents duplication
                    if existing_bills_count == 0:
                        if outstanding_amount > 0:
                            c.execute('''INSERT INTO billing
                                         (customer_id, month, year, amount, is_paid, is_manual, outstanding_amount)
                                         VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                      (customer_id, self.current_month, self.current_year,
                                       outstanding_amount, 0, 1, outstanding_amount))
                        elif credit_balance > 0:
                            c.execute('''INSERT INTO billing
                                         (customer_id, month, year, amount, is_paid, is_manual, credit_amount)
                                         VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                      (customer_id, self.current_month, self.current_year,
                                       credit_balance, 0, 1, credit_balance))

            conn.commit()
            conn.close()
        except Exception as e:
            logging.error(f"Error ensuring monthly bills: {str(e)}")
            if 'conn' in locals():
                conn.close()

    def _generate_invoice_number(self):
        db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        
        c.execute("SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER)) FROM billing WHERE invoice_number IS NOT NULL")
        max_inv_num = c.fetchone()[0] or 0
        conn.close()
        
        return f"INV-{str(max_inv_num + 1).zfill(4)}"

    def _apply_credit_to_future_bills(self, customer_id):
        """Uses a customer's credit balance to pay for future monthly bills automatically.
        Returns the amount of credit actually used."""
        conn = None
        credit_used = 0
        try:
            db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
            conn = sqlite3.connect(db_path)
            c = conn.cursor()
            db = Database()

            # Get customer's package price
            c.execute("SELECT package_id FROM customers WHERE id = ?", (customer_id,))
            result = c.fetchone()
            if not result:
                return 0
            package_id = result[0]
            if not package_id:
                return 0
            c.execute("SELECT price FROM packages WHERE id = ?", (package_id,))
            package_price_result = c.fetchone()
            if not package_price_result or package_price_result[0] <= 0:
                return 0
            package_price = package_price_result[0]
            payment_date = datetime.now().strftime('%Y-%m-%d')

            # Get the most recent bill to determine next month
            c.execute("SELECT year, month FROM billing WHERE customer_id = ? ORDER BY year DESC, month DESC LIMIT 1", (customer_id,))
            last_bill = c.fetchone()
            if last_bill:
                current_year, current_month = last_bill
            else:
                current_year = datetime.now().year
                current_month = datetime.now().month

            # Use imported outstanding first, then imported credit, then bill credit
            used_outstanding, imported_credit_left = db.use_imported_outstanding(customer_id, float('inf'))
            imported_credit_used, imported_credit_left = db.use_imported_credit(customer_id, imported_credit_left)
            # Now get remaining credit from last paid bill (credit_amount)
            c.execute('''
                SELECT id, credit_amount FROM billing
                WHERE customer_id = ? AND status = 'Paid'
                ORDER BY year DESC, month DESC, id DESC
                LIMIT 1
            ''', (customer_id,))
            last_bill_credit = c.fetchone()
            last_paid_bill_id = last_bill_credit[0] if last_bill_credit else None
            last_bill_credit_amount = last_bill_credit[1] if last_bill_credit and last_bill_credit[1] is not None else 0.0
            total_credit = imported_credit_used + last_bill_credit_amount
            # Only use credit for future bills, not outstanding
            while total_credit >= package_price:
                current_month += 1
                if current_month > 12:
                    current_month = 1
                    current_year += 1
                c.execute("SELECT id, status FROM billing WHERE customer_id = ? AND month = ? AND year = ?", (customer_id, current_month, current_year))
                existing_bill = c.fetchone()
                future_bill_id = None
                if existing_bill:
                    if existing_bill[1] == 'Paid':
                        break
                    future_bill_id = existing_bill[0]
                else:
                    c.execute('''INSERT INTO billing 
                                 (customer_id, month, year, amount, is_paid, is_manual, credit_amount)
                                 VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                (customer_id, current_month, current_year, 
                                package_price, 1, 1, 0))
                total_credit -= package_price
            credit_used = total_credit

            # Update the last paid bill's credit_amount if any left
            if last_paid_bill_id is not None:
                c.execute('''UPDATE billing 
                             SET credit_amount = ?
                             WHERE id = ?''',
                          (max(0, total_credit), last_paid_bill_id))

            conn.commit()
            return credit_used
        except Exception as e:
            logging.error(f"Error applying credit to future bills for customer {customer_id}: {e}")
            if conn:
                conn.rollback()
            return 0
        finally:
            if conn:
                conn.close()

    def _process_payment(self, payment_var, unpaid_bills, total_payable, payment_dialog, customer_id):
        db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
        try:
            with sqlite3.connect(db_path) as conn:
                c = conn.cursor()
                payment_amount = float(payment_var.get())
                if payment_amount <= 0:
                    raise ValueError("Payment amount must be positive")

                # 1. Get current credit balance
                c.execute("SELECT credit_balance FROM customers WHERE id = ?", (customer_id,))
                current_credit = c.fetchone()[0] or 0.0

                payment_date = datetime.now().strftime('%Y-%m-%d')
                current_username = current_user if current_user else "Unknown"
                invoice_number = self._generate_invoice_number()

                db = Database()
                # Use imported outstanding first, then imported credit, then bill credit
                used_outstanding, remaining_amount = db.use_imported_outstanding(customer_id, payment_amount)
                imported_credit_used, remaining_amount = db.use_imported_credit(customer_id, remaining_amount)
                
                # 2. Apply payment logic with enhanced credit handling
                if current_credit > 0:
                    # Use available credit first
                    credit_used = min(current_credit, remaining_amount)
                    remaining_amount -= credit_used
                    
                    # Update customer credit (set to zero if fully utilized)
                    new_credit = max(0, current_credit - credit_used)
                    c.execute("UPDATE customers SET credit_balance = ? WHERE id = ?", 
                             (new_credit, customer_id))
                    
                    # If credit was fully used, ensure no carry forward
                    if new_credit == 0:
                        c.execute('''UPDATE billing 
                                    SET credit_amount = 0
                                    WHERE customer_id = ?''', (customer_id,))
                
                # Now get remaining credit from last paid bill (credit_amount)
                c.execute('''
                    SELECT id, credit_amount FROM billing
                    WHERE customer_id = ? AND status = 'Paid'
                    ORDER BY year DESC, month DESC, id DESC
                    LIMIT 1
                ''', (customer_id,))
                last_bill_credit = c.fetchone()
                last_paid_bill_id = last_bill_credit[0] if last_bill_credit else None
                last_bill_credit_amount = last_bill_credit[1] if last_bill_credit and last_bill_credit[1] is not None else 0.0
                credit_used_from_bill = 0.0
                if remaining_amount > 0 and last_bill_credit_amount > 0:
                    credit_used_from_bill = min(last_bill_credit_amount, remaining_amount)
                    c.execute('''UPDATE billing SET credit_amount = credit_amount - ? WHERE id = ?''', (credit_used_from_bill, last_paid_bill_id))
                    remaining_amount -= credit_used_from_bill
                    
                    # If bill credit was fully used, ensure no carry forward
                    if credit_used_from_bill >= last_bill_credit_amount:
                        c.execute('''UPDATE billing 
                                    SET credit_amount = 0
                                    WHERE customer_id = ?''', (customer_id,))
                
                total_credit_used = imported_credit_used + credit_used_from_bill
                actual_amount = max(0, payment_amount - (used_outstanding + total_credit_used))

                remaining_payment = actual_amount
                payment_details = []
                credit_amount = 0
                outstanding_amount = 0

                for bill in unpaid_bills:
                    if remaining_payment <= 0:
                        break

                    bill_id, month, year, pending, original_amount, is_manual = bill
                    amount_to_apply = min(remaining_payment, pending)

                    new_paid_amount = amount_to_apply
                    if pending == amount_to_apply:
                        status = 'Paid'
                    else:
                        status = 'Partially Paid'

                    c.execute('''
                        UPDATE billing 
                        SET paid_amount = COALESCE(paid_amount, 0) + ?,
                            status = ?,
                            paid_date = ?,
                            invoice_number = ?,
                            paid_by = ?
                        WHERE id = ?
                    ''', (new_paid_amount, status, payment_date, invoice_number, current_username, bill_id))

                    payment_details.append({
                        'month': f"{month:02d}/{year}",
                        'amount_paid': new_paid_amount,
                        'status': status
                    })

                    remaining_payment -= new_paid_amount

                if remaining_payment > 0:
                    credit_amount = remaining_payment
                    # Add the credit to customer's balance
                    c.execute('''UPDATE customers SET credit_balance = COALESCE(credit_balance, 0) + ? WHERE id = ?''', 
                             (credit_amount, customer_id))
                else:
                    outstanding_amount = total_payable - payment_amount

                c.execute('''
                    INSERT INTO payment_history (
                        invoice_number, customer_id, payment_date, amount_paid,
                        credit_amount, outstanding_amount, paid_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (invoice_number, customer_id, payment_date, payment_amount,
                      credit_amount, outstanding_amount, current_username))

                if outstanding_amount > 0:
                    c.execute('''
                        UPDATE billing 
                        SET outstanding_amount = ?
                        WHERE id = (
                            SELECT id FROM billing 
                            WHERE customer_id = ? AND status != 'Paid'
                            ORDER BY year DESC, month DESC 
                            LIMIT 1
                        )
                    ''', (outstanding_amount, customer_id))

                # Sync customer financial data (preserve imported values)
                self.db.sync_customer_financial_data(customer_id, preserve_imported_values=True)
                conn.commit()

                # After payment and credit are committed, apply credit to future bills
                if credit_amount > 0:
                    credit_used = self._apply_credit_to_future_bills(customer_id)
                    if credit_used > 0:
                        # Update the payment history to reflect credit used
                        c.execute('''
                            UPDATE payment_history 
                            SET credit_amount = credit_amount - ?
                            WHERE invoice_number = ? AND customer_id = ?
                        ''', (credit_used, invoice_number, customer_id))
                        conn.commit()

                summary = "Payment Applied:\n"
                for detail in payment_details:
                    summary += f"{detail['month']}: {detail['amount_paid']:.2f} - {detail['status']}\n"

                if used_outstanding > 0:
                    summary += f"\nOutstanding Used: {used_outstanding:.2f} PKR"
                if total_credit_used > 0:
                    summary += f"\nCredit Used: {total_credit_used:.2f} PKR"
                if credit_amount > 0:
                    summary += f"\nInitial Credit Amount: {credit_amount:.2f} PKR"
                    if credit_used > 0:
                        summary += f"\nCredit Applied to Future Bills: {credit_used:.2f} PKR"
                        summary += f"\nRemaining Credit: {credit_amount - credit_used:.2f} PKR"
                if outstanding_amount > 0:
                    summary += f"\nOutstanding Amount: {outstanding_amount:.2f} PKR"

                messagebox.showinfo("Payment Successful", summary)

                self.payment_receipt.ask_for_receipt(
                    customer_id=customer_id,
                    amount=payment_amount,
                    payment_date=payment_date,
                    total_pending=total_payable,
                    outstanding_amount=outstanding_amount,
                    bill_id=bill_id
                )

                payment_dialog.destroy()
                self._load_bills()

        except ValueError as ve:
            messagebox.showerror("Error", str(ve))
        except Exception as e:
            logging.error(f"Payment processing error: {str(e)}")
            messagebox.showerror("Error", f"Payment failed: {str(e)}")

    def _mark_paid(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a bill to mark as paid")
            return

        bill_id = int(self.tree.item(selected[0])['values'][0])
        db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
        try:
            with sqlite3.connect(db_path) as conn:
                c = conn.cursor()
                c.execute("SELECT customer_id FROM billing WHERE id = ?", (bill_id,))
                customer_id = c.fetchone()[0]
                c.execute('''
                    SELECT b.id, b.month, b.year, 
                           b.amount - COALESCE(b.paid_amount, 0) as pending,
                           b.amount as original_amount,
                           b.is_manual
                    FROM billing b
                    WHERE b.customer_id = ? AND (b.amount - COALESCE(b.paid_amount, 0)) > 0
                    ORDER BY b.year, b.month ASC
                ''', (customer_id,))
                unpaid_bills = c.fetchall()
                if not unpaid_bills:
                    messagebox.showinfo("Info", "No pending bills found")
                    return
                total_payable = sum(bill[3] for bill in unpaid_bills)
        except Exception as e:
            logging.error(f"Error in payment dialog: {str(e)}")
            messagebox.showerror("Error", f"Failed to process payment: {str(e)}")
            return
        payment_dialog = tk.Toplevel(self)
        payment_dialog.title("Payment Processing")
        payment_dialog.geometry("600x500")
        payment_dialog.transient(self)
        payment_dialog.grab_set()
        payment_dialog.update_idletasks()
        width = payment_dialog.winfo_width()
        height = payment_dialog.winfo_height()
        x = (payment_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (payment_dialog.winfo_screenheight() // 2) - (height // 2)
        payment_dialog.geometry(f"{width}x{height}+{x}+{y}")
        main_frame = tk.Frame(payment_dialog, bg=self.COLORS['card_bg'], highlightbackground=self.COLORS['border'], 
                            highlightthickness=2, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)
        tk.Label(main_frame, text="Pending Bills Breakdown", font=("Helvetica", 14, "bold"), 
                bg=self.COLORS['card_bg'], fg=self.COLORS['primary_accent']).pack(pady=10)
        columns = ("Month", "Bill Type", "Amount", "Status")
        tree = ttk.Treeview(main_frame, columns=columns, show="headings", height=5)
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor="center")
        for bill in unpaid_bills:
            bill_id, month, year, pending, original_amount, is_manual = bill
            bill_type = "Manual" if is_manual else "Package"
            tree.insert("", "end", values=(
                f"{month:02d}/{year}", 
                bill_type,
                f"{pending:.2f}",
                "Unpaid"
            ))
        tree.pack(fill="x", pady=10)
        tk.Label(main_frame, text=f"Total Payable Amount: {total_payable:.2f} PKR", 
                font=("Helvetica", 12, "bold"), bg=self.COLORS['card_bg'], fg=self.COLORS['text_primary']).pack(pady=5)
        payment_container = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        payment_container.pack(pady=10)
        tk.Label(payment_container, text="Payment Amount:", bg=self.COLORS['card_bg'], 
                font=("Helvetica", 10), fg=self.COLORS['text_primary']).pack(side="left", padx=5)
        payment_var = tk.StringVar()
        payment_entry = tk.Entry(payment_container, textvariable=payment_var, 
                               font=("Helvetica", 10), width=15)
        payment_entry.pack(side="left", padx=5)
        button_container = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        button_container.pack(pady=10)
        button_subframe = tk.Frame(button_container, bg=self.COLORS['card_bg'])
        button_subframe.pack()
        pay_full_btn = tk.Button(button_subframe, text="Pay Full", 
                               font=("Helvetica", 10, "bold"),
                               command=lambda: payment_var.set(str(total_payable)),
                               bg=self.COLORS['primary_accent'], fg="#FFFFFF",
                               relief=tk.FLAT, padx=20, pady=5,
                               activebackground=self.COLORS['secondary_accent'], 
                               activeforeground="#FFFFFF")
        pay_full_btn.pack(side="left", padx=10)
        process_btn = tk.Button(button_subframe, text="Process Payment", 
                              font=("Helvetica", 10, "bold"),
                              command=lambda: self._process_payment(payment_var, unpaid_bills, total_payable, 
                                                                  payment_dialog, customer_id),
                              bg=self.COLORS['primary_accent'], fg="#FFFFFF",
                              relief=tk.FLAT, padx=20, pady=5,
                              activebackground=self.COLORS['secondary_accent'], 
                              activeforeground="#FFFFFF")
        process_btn.pack(side="left", padx=10)

    def _add_manual_bill(self):
        dialog = tk.Toplevel(self)
        dialog.title("Add Manual Bill")
        dialog.transient(self)
        dialog.grab_set()
        dialog.configure(bg=self.COLORS['background'])
        
        # Make the dialog larger and resizable
        dialog.geometry("900x700")
        dialog.minsize(800, 600)
        
        # Center the dialog
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")

        # Create a main container frame with scrollbar
        main_container = tk.Frame(dialog, bg=self.COLORS['background'])
        main_container.pack(fill="both", expand=True)

        # Create a canvas
        canvas = tk.Canvas(main_container, bg=self.COLORS['background'])
        canvas.pack(side="left", fill="both", expand=True)

        # Add a scrollbar
        scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=canvas.yview)
        scrollbar.pack(side="right", fill="y")

        # Configure the canvas
        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.bind('<Configure>', lambda e: canvas.configure(scrollregion=canvas.bbox("all")))

        # Create another frame inside the canvas
        main_frame = tk.Frame(canvas, bg=self.COLORS['card_bg'], highlightbackground=self.COLORS['border'], 
                             highlightthickness=2, padx=20, pady=20)
        canvas.create_window((0, 0), window=main_frame, anchor="nw")

        # Make mouse wheel scroll work
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        tk.Label(main_frame, text="Add Manual Bill", bg=self.COLORS['card_bg'], 
                font=("Helvetica", 16, "bold"), fg=self.COLORS['primary_accent']).pack(anchor="center", pady=(0, 20))

        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        dropdown_style = {"font": ("Helvetica", 12), "width": 10}
        entry_style = {"font": ("Helvetica", 12), "width": 30}

        customer_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        customer_frame.pack(fill="x", pady=(0, 15))
        tk.Label(customer_frame, text="Select Customer:", **label_style, width=15, anchor="w").pack(side="left")
        customer_var = tk.StringVar()
        customer_dropdown = ttk.Combobox(customer_frame, textvariable=customer_var, values=list(self.customers.keys()),
                                         state="readonly", **entry_style)
        customer_dropdown.pack(side="left")

        month_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        month_frame.pack(fill="x", pady=(0, 15))
        tk.Label(month_frame, text="Month:", **label_style, width=15, anchor="w").pack(side="left")
        month_var = tk.StringVar(value=str(self.current_month))
        month_dropdown = ttk.Combobox(month_frame, textvariable=month_var, values=[str(i) for i in range(1, 13)],
                                      state="readonly", **dropdown_style)
        month_dropdown.pack(side="left")

        year_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        year_frame.pack(fill="x", pady=(0, 15))
        tk.Label(year_frame, text="Year:", **label_style, width=15, anchor="w").pack(side="left")
        year_var = tk.StringVar(value=str(self.current_year))
        year_dropdown = ttk.Combobox(year_frame, textvariable=year_var, values=[str(i) for i in range(2000, 2101)],
                                     state="readonly", **dropdown_style)
        year_dropdown.pack(side="left")

        amount_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        amount_frame.pack(fill="x", pady=(0, 15))
        tk.Label(amount_frame, text="Amount (PKR):", **label_style, width=15, anchor="w").pack(side="left")
        amount_var = tk.StringVar()
        amount_entry = tk.Entry(amount_frame, textvariable=amount_var, **entry_style)
        amount_entry.pack(side="left", padx=5)

        # Outstanding Bills Breakdown Section
        outstanding_frame = ttk.LabelFrame(main_frame, text="Outstanding Bills Breakdown", padding=10)
        outstanding_frame.pack(fill="x", pady=(10, 5))

        self.outstanding_tree = ttk.Treeview(
            outstanding_frame,
            columns=("id", "month", "year", "amount", "pending"),
            show="headings",
            height=4,
            selectmode="none"  # Initially disabled until selective inclusion is enabled
        )
        self.outstanding_tree.heading("id", text="Bill ID")
        self.outstanding_tree.heading("month", text="Month")
        self.outstanding_tree.heading("year", text="Year")
        self.outstanding_tree.heading("amount", text="Original Amount")
        self.outstanding_tree.heading("pending", text="Pending Amount")
        self.outstanding_tree.column("id", width=60, anchor="center")
        self.outstanding_tree.column("month", width=80, anchor="center")
        self.outstanding_tree.column("year", width=80, anchor="center")
        self.outstanding_tree.column("amount", width=100, anchor="e")
        self.outstanding_tree.column("pending", width=100, anchor="e")
        
        scrollbar = ttk.Scrollbar(outstanding_frame, orient="vertical", command=self.outstanding_tree.yview)
        self.outstanding_tree.configure(yscrollcommand=scrollbar.set)
        self.outstanding_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Checkbox for selective inclusion
        self.selective_include_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(
            outstanding_frame,
            text="Select specific bills to include",
            variable=self.selective_include_var,
            command=lambda: self._toggle_selective_include(customer_var, month_var, year_var, amount_var)
        ).pack(anchor="w", pady=(5, 0))

        # Payment History Preview
        history_frame = ttk.LabelFrame(main_frame, text="Recent Payment History", padding=10)
        history_frame.pack(fill="x", pady=(5, 10))

        self.history_tree = ttk.Treeview(
            history_frame,
            columns=("date", "amount", "method", "invoice"),
            show="headings",
            height=3
        )
        self.history_tree.heading("date", text="Date")
        self.history_tree.heading("amount", text="Amount")
        self.history_tree.heading("method", text="Method")
        self.history_tree.heading("invoice", text="Invoice #")
        self.history_tree.column("date", width=100)
        self.history_tree.column("amount", width=80, anchor="e")
        self.history_tree.column("method", width=100)
        self.history_tree.column("invoice", width=120)
        self.history_tree.pack(fill="x")

        # Summary Section
        summary_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        summary_frame.pack(fill="x", pady=(5, 10))

        self.summary_label = tk.Label(
            summary_frame,
            text="Summary:\nTotal Outstanding: PKR 0.00\nSelected Outstanding: PKR 0.00\nManual Amount: PKR 0.00\nTotal Bill: PKR 0.00",
            bg=self.COLORS['card_bg'],
            font=("Helvetica", 10),
            justify="left"
        )
        self.summary_label.pack(anchor="w")

        # Scenario detection label
        self.scenario_label = tk.Label(
            summary_frame,
            text="Scenario: Select a customer",
            bg=self.COLORS['card_bg'],
            font=("Helvetica", 10, "italic")
        )
        self.scenario_label.pack(anchor="w", pady=(5, 0))

        # Set up auto-update triggers
        for var in [customer_var, month_var, year_var, amount_var]:
            var.trace_add('write', lambda *_: self._update_bill_details(customer_var, month_var, year_var, amount_var))

        # Add the button at the bottom with some padding
        button_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        button_frame.pack(fill="x", pady=(20, 10))
        
        tk.Button(button_frame, text="Add Bill", 
                 command=lambda: self._submit_manual_bill(dialog, customer_var, month_var, year_var, amount_var),
                 bg=self.COLORS['primary_accent'], fg="#FFFFFF",
                 font=("Helvetica", 12, "bold"), relief=tk.FLAT, padx=20, pady=10,
                 activebackground=self.COLORS['secondary_accent'], activeforeground="#FFFFFF").pack()

        # Initial data load
        self._update_bill_details(customer_var, month_var, year_var, amount_var)

    def _toggle_selective_include(self, customer_var, month_var, year_var, amount_var):
        if self.selective_include_var.get():
            self.outstanding_tree.config(selectmode="extended")
        else:
            self.outstanding_tree.config(selectmode="none")
        self._update_bill_details(customer_var, month_var, year_var, amount_var)

    def _update_bill_details(self, customer_var, month_var, year_var, amount_var):
        try:
            customer_name = customer_var.get()
            if not customer_name or customer_name not in self.customers:
                self.summary_label.config(text="Summary:\nTotal Outstanding: PKR 0.00\nSelected Outstanding: PKR 0.00\nManual Amount: PKR 0.00\nTotal Bill: PKR 0.00")
                self.scenario_label.config(text="Scenario: Select a customer")
                for item in self.outstanding_tree.get_children():
                    self.outstanding_tree.delete(item)
                for item in self.history_tree.get_children():
                    self.history_tree.delete(item)
                return

            customer_id = self.customers[customer_name]
            db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
            conn = sqlite3.connect(db_path)
            c = conn.cursor()
            db = Database()
            # ... existing code ...
            try:
                manual_amount = float(amount_var.get()) if amount_var.get() else 0.0
            except ValueError:
                manual_amount = 0.0

            selected_outstanding = 0.0
            if self.selective_include_var.get():
                for item in self.outstanding_tree.selection():
                    values = self.outstanding_tree.item(item)['values']
                    selected_outstanding += float(values[4])
            else:
                for item in self.outstanding_tree.get_children():
                    values = self.outstanding_tree.item(item)['values']
                    selected_outstanding += float(values[4])

            total_amount = manual_amount + selected_outstanding

            # Use imported outstanding first, then imported credit, then bill credit
            used_outstanding, imported_credit_left = db.use_imported_outstanding(customer_id, float('inf'))
            imported_credit_used, imported_credit_left = db.use_imported_credit(customer_id, imported_credit_left)
            # Now get remaining credit from last paid bill (credit_amount)
            c.execute('''
                SELECT id, credit_amount FROM billing
                WHERE customer_id = ? AND status = 'Paid'
                ORDER BY year DESC, month DESC, id DESC
                LIMIT 1
            ''', (customer_id,))
            last_bill_credit = c.fetchone()
            last_paid_bill_id = last_bill_credit[0] if last_bill_credit else None
            last_bill_credit_amount = last_bill_credit[1] if last_bill_credit and last_bill_credit[1] is not None else 0.0
            total_credit = imported_credit_used + last_bill_credit_amount
            # Only use credit for future bills, not outstanding
            while total_credit >= package_price:
                current_month += 1
                if current_month > 12:
                    current_month = 1
                    current_year += 1
                c.execute("SELECT id, status FROM billing WHERE customer_id = ? AND month = ? AND year = ?", (customer_id, current_month, current_year))
                existing_bill = c.fetchone()
                future_bill_id = None
                if existing_bill:
                    if existing_bill[1] == 'Paid':
                        break
                    future_bill_id = existing_bill[0]
                else:
                    c.execute('''INSERT INTO billing 
                                 (customer_id, month, year, amount, is_paid, is_manual, credit_amount)
                                 VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                (customer_id, current_month, current_year, 
                                package_price, 1, 1, 0))
                total_credit -= package_price
            credit_used = total_credit

            conn.commit()
            conn.close()
        except Exception as e:
            logging.error(f"Error ensuring monthly bills: {str(e)}")
            if 'conn' in locals():
                conn.close()

    def _submit_manual_bill(self, dialog, customer_var, month_var, year_var, amount_var):
        try:
            customer_name = customer_var.get()
            if not customer_name or customer_name not in self.customers:
                raise ValueError("Please select a valid customer")

            month = int(month_var.get())
            year = int(year_var.get())
            amount = float(amount_var.get())

            if amount <= 0:
                raise ValueError("Amount must be greater than 0")

            customer_id = self.customers[customer_name]

            db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
            conn = sqlite3.connect(db_path)
            c = conn.cursor()

            # Check if bill already exists for this customer/month/year
            c.execute('''SELECT id FROM billing 
                         WHERE customer_id = ? AND month = ? AND year = ? AND is_manual = 1''',
                      (customer_id, month, year))
            if c.fetchone():
                raise ValueError("A manual bill already exists for this customer and period")

            # Use imported outstanding first, then imported credit, then bill credit
            db = Database()
            used_outstanding, remaining_amount = db.use_imported_outstanding(customer_id, amount)
            imported_credit_used, remaining_amount = db.use_imported_credit(customer_id, remaining_amount)
            # Now check for credit from last paid bill
            c.execute('''
                SELECT id, credit_amount FROM billing
                WHERE customer_id = ? AND status = 'Paid'
                ORDER BY year DESC, month DESC, id DESC
                LIMIT 1
            ''', (customer_id,))
            last_bill_credit = c.fetchone()
            last_paid_bill_id = last_bill_credit[0] if last_bill_credit else None
            last_bill_credit_amount = last_bill_credit[1] if last_bill_credit and last_bill_credit[1] is not None else 0.0
            credit_used_from_bill = 0.0
            if remaining_amount > 0 and last_bill_credit_amount > 0:
                credit_used_from_bill = min(last_bill_credit_amount, remaining_amount)
                c.execute('''UPDATE billing SET credit_amount = credit_amount - ? WHERE id = ?''', (credit_used_from_bill, last_paid_bill_id))
                remaining_amount -= credit_used_from_bill
            total_credit_used = imported_credit_used + credit_used_from_bill
            actual_amount = max(0, amount - (used_outstanding + total_credit_used))

            # Insert the manual bill, recording the amounts used
            is_paid = 1 if actual_amount == 0 else 0
            status = 'Paid' if is_paid else 'Unpaid'

            c.execute('''INSERT INTO billing 
                         (customer_id, month, year, amount, is_paid, is_manual, status,
                          outstanding_amount, credit_amount)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                      (customer_id, month, year, amount, is_paid, 1, status,
                       used_outstanding if used_outstanding > 0 else 0,
                       total_credit_used if total_credit_used > 0 else 0))

            # Update the customer's outstanding and credit balances
            c.execute('''UPDATE customers SET outstanding_amount = outstanding_amount - ?, credit_balance = credit_balance - ? WHERE id = ?''',
                      (used_outstanding, imported_credit_used, customer_id))

            if is_paid:
                invoice_number = self._generate_invoice_number()
                c.execute('''UPDATE billing 
                             SET paid_amount = ?, paid_date = ?, invoice_number = ?, paid_by = ?
                             WHERE id = ?''',
                          (amount, datetime.now().strftime('%Y-%m-%d'), 
                           invoice_number, current_user if current_user else "Manual Bill", 
                           c.lastrowid))

                c.execute('''
                    INSERT INTO payment_history (
                        invoice_number, customer_id, payment_date, amount_paid,
                        credit_amount, outstanding_amount, paid_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (invoice_number, customer_id, datetime.now().strftime('%Y-%m-%d'), 
                     amount, -total_credit_used, 0, current_user if current_user else "Manual Bill"))

            # Sync customer financial data (preserve imported values)
            self.db.sync_customer_financial_data(customer_id, preserve_imported_values=True)
            conn.commit()
            conn.close()

            messagebox.showinfo("Success", "Manual bill added successfully")
            dialog.destroy()
            self._load_bills()

        except ValueError as ve:
            messagebox.showerror("Error", str(ve))
        except Exception as e:
            logging.error(f"Error adding manual bill: {str(e)}")
            messagebox.showerror("Error", f"Failed to add manual bill: {str(e)}")
            if 'conn' in locals():
                conn.rollback()

    def refresh(self):
        self._load_customers()
        self._load_bills()

    def set_customer(self, customer_id):
        self.customer_id = customer_id
        self.specific_customer_view = True
        
        db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        c.execute("SELECT name FROM customers WHERE id = ?", (customer_id,))
        customer_name = c.fetchone()
        conn.close()
        
        if customer_name:
            self.customer_search_var.set(customer_name[0])
            self.customer_combobox.config(state='disabled')
        
        self._load_bills()

    def _delete_bill(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a bill to delete")
            return

        bill_id = int(self.tree.item(selected[0])['values'][0])
        customer = self.tree.item(selected[0])['values'][2]
        month = self.tree.item(selected[0])['values'][5]

        if not messagebox.askyesno("Confirm", f"Delete bill for {customer} ({month})?\nThis cannot be undone"):
            return

        try:
            db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
            conn = sqlite3.connect(db_path)
            c = conn.cursor()

            c.execute('''DELETE FROM billing WHERE id = ?''', (bill_id,))
            conn.commit()
            conn.close()
            messagebox.showinfo("Success", "Bill deleted successfully")
            self._load_bills()
        except Exception as e:
            logging.error(f"Error deleting bill: {str(e)}")
            messagebox.showerror("Error", f"Failed to delete bill: {str(e)}")
