import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from database_utils import get_db_connection
import os
from PIL import Image, ImageTk
from resources import resource_path
import re

class StockManager(tk.Frame):
    COLORS = {
        'background': '#F8F9FA',        # Light gray background
        'card_bg': '#FFFFFF',           # White for cards
        'primary_accent': '#4A6FA5',    # Primary blue accent
        'secondary_accent': '#6C8FC7',  # Lighter blue for hover
        'text_primary': '#2D3748',      # Dark gray for primary text
        'text_secondary': '#718096',    # Lighter gray for secondary text
        'button_start': '#4A6FA5',      # Button default color
        'button_end': '#3A5A8C',        # Button hover/pressed color
        'border': '#E2E8F0',            # Light gray border
        'warning': '#E53E3E',           # Red for warnings/errors
        'input_bg': '#EDF2F7',          # Light gray for input fields
        'bar_colors': [
            '#4A6FA5',  # Primary accent
            '#6C8FC7',  # Secondary accent
            '#FFC107',  # Warning yellow
            '#E53E3E',  # Danger red
            '#2196F3',  # Info blue
            '#9E9E9E'   # Gray
        ],
        'pie_color1': '#4A6FA5',        # Primary accent for Paid
        'pie_color2': '#E53E3E',        # Danger red for Unpaid
        'pie_divider': '#FFFFFF',       # White
        'line_chart': '#4A6FA5',        # Primary accent
        'div_background': '#FFFFFF',    # White for div backgrounds
        'button_border': '#E2E8F0'      # Muted gray
    }

    def __init__(self, parent, nav_commands):
        super().__init__(parent)
        self.nav = nav_commands
        self.db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
        self._setup_ui()

    def _setup_ui(self):
        self.config(bg=self.COLORS['background'])
        self.pack(fill="both", expand=True)
        self._create_header()
        self._create_stock_table()

    def _create_header(self):
        header = tk.Frame(self, bg=self.COLORS['primary_accent'], height=90)
        header.pack(fill="x", pady=(0, 20))

        # MODIFIED: Icon loading with resource_path
        def load_and_process_image(relative_path):
            try:
                full_path = resource_path(relative_path)
                if os.path.exists(full_path):
                    img = Image.open(full_path).convert("RGBA")
                    data = img.getdata()
                    new_data = []
                    for item in data:
                        if item[3] > 0:  # If pixel is not transparent
                            new_data.append((255, 255, 255, item[3]))  # Set to white
                        else:
                            new_data.append(item)
                    img.putdata(new_data)
                    img = img.resize((30, 30), Image.Resampling.LANCZOS)
                    return ImageTk.PhotoImage(img)
                else:
                    print(f"Icon not found: {full_path}")
                    return ImageTk.PhotoImage(Image.new('RGBA', (30, 30), (255, 255, 255, 0)))
            except Exception as e:
                print(f"Error loading image: {str(e)}")
                return ImageTk.PhotoImage(Image.new('RGBA', (30, 30), (255, 255, 255, 0)))

        # Load all icons with resource_path
        self.dashboard_img = load_and_process_image('assets/dashboard/stock/dashboard.png')
        self.add_img = load_and_process_image('assets/dashboard/stock/addstock.png')
        self.delete_img = load_and_process_image('assets/dashboard/stock/Deletestock.png')
        self.delete_all_img = load_and_process_image('assets/dashboard/stock/deleteallstock.png')

        # Dashboard button
        dashboard_btn = tk.Button(header, image=self.dashboard_img, command=self.nav['show_dashboard'],
                                bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                activebackground=self.COLORS['secondary_accent'],
                                width=30, height=30)
        dashboard_btn.pack(side="left", padx=10)
        Tooltip(dashboard_btn, "Dashboard")

        header_label = tk.Label(header, text="Stock Management", font=("Helvetica", 24, "bold"),
                               fg="#FFFFFF", bg=self.COLORS['primary_accent'])
        header_label.place(relx=0.5, rely=0.5, anchor="center")

        button_frame = tk.Frame(header, bg=self.COLORS['primary_accent'])
        button_frame.pack(side="right", padx=10)

        # Add button
        add_btn = tk.Button(button_frame, image=self.add_img, command=self._add_stock,
                           bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                           activebackground=self.COLORS['secondary_accent'],
                           width=30, height=30)
        add_btn.pack(side="left", padx=5)
        Tooltip(add_btn, "Add Stock")

        # Delete button
        delete_btn = tk.Button(button_frame, image=self.delete_img, command=self._delete_stock,
                              bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                              activebackground=self.COLORS['secondary_accent'],
                              width=30, height=30)
        delete_btn.pack(side="left", padx=5)
        Tooltip(delete_btn, "Delete Stock")

        # Delete All button
        delete_all_btn = tk.Button(button_frame, image=self.delete_all_img, command=self._delete_all_stock,
                                  bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                  activebackground=self.COLORS['secondary_accent'],
                                  width=30, height=30)
        delete_all_btn.pack(side="left", padx=5)
        Tooltip(delete_all_btn, "Delete All Stock")

    def _create_stock_table(self):
        content = tk.Frame(self, bg=self.COLORS['div_background'])
        content.pack(fill="both", expand=True, padx=5, pady=5)

        tree_frame = tk.Frame(content, bg=self.COLORS['div_background'])
        tree_frame.pack(fill="both", expand=True, pady=(0, 5))

        style = ttk.Style()
        style.configure("Treeview",
                        background=self.COLORS['div_background'],
                        foreground=self.COLORS['text_primary'],
                        fieldbackground=self.COLORS['div_background'],
                        font=("Helvetica", 10))
        style.configure("Treeview.Heading",
                        font=("Helvetica", 10, "bold"),
                        foreground=self.COLORS['text_primary'])
        style.map("Treeview", background=[('selected', self.COLORS['primary_accent'])])
        style.layout("Treeview", [('Treeview.treearea', {'sticky': 'nswe'})])
        style.configure("Treeview", borderwidth=1, relief="solid", bordercolor=self.COLORS['border'])
        style.configure("Treeview", rowheight=25)

        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL)
        scrollbar.pack(side="right", fill="y")

        self.tree = ttk.Treeview(tree_frame, 
                               columns=("id", "product_id", "name", "quantity", "sold", "remaining"),
                               selectmode="browse", show="headings", yscrollcommand=scrollbar.set,
                               style="Treeview")
        scrollbar.config(command=self.tree.yview)

        self.tree.heading("id", text="ID", anchor="center")
        self.tree.heading("product_id", text="Product ID", anchor="center")
        self.tree.heading("name", text="Product Name", anchor="center")
        self.tree.heading("quantity", text="Total Quantity", anchor="center")
        self.tree.heading("sold", text="Sold", anchor="center")
        self.tree.heading("remaining", text="Remaining", anchor="center")

        self.tree.column("id", width=50, anchor="center")
        self.tree.column("product_id", width=80, anchor="center")
        self.tree.column("name", width=150, anchor="center")
        self.tree.column("quantity", width=100, anchor="center")
        self.tree.column("sold", width=80, anchor="center")
        self.tree.column("remaining", width=100, anchor="center")

        self.tree.pack(fill="both", expand=True)
        self._load_stock()

    def _load_stock(self):
        try:
            conn = get_db_connection().__enter__()
            c = conn.cursor()

            # Ensure all products have stock entries
            c.execute("SELECT id FROM products")
            product_ids = [row[0] for row in c.fetchall()]
            
            for product_id in product_ids:
                c.execute("SELECT 1 FROM stock WHERE product_id = ?", (product_id,))
                if not c.fetchone():
                    c.execute("INSERT INTO stock (product_id, quantity, sold) VALUES (?, ?, ?)",
                             (product_id, 0, 0))
            
            conn.commit()

            # Load stock data with product names
            c.execute('''SELECT s.id, s.product_id, p.name, s.quantity, s.sold, 
                        (s.quantity - s.sold) as remaining
                        FROM stock s
                        JOIN products p ON s.product_id = p.id
                        ORDER BY p.name''')
            stock_items = c.fetchall()

            for item in self.tree.get_children():
                self.tree.delete(item)

            for item in stock_items:
                self.tree.insert("", "end", values=item)

            conn.close()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load stock: {str(e)}")

    def refresh_stock(self):
        self._load_stock()

    def _validate_input(self, char, current_text):
        """Validate input to allow only alphanumeric characters and limit to 25 characters."""
        if len(current_text) >= 25:
            return False
        return bool(re.match(r'^[a-zA-Z0-9\s]*$', char))

    def _add_stock(self):
        dialog = tk.Toplevel(self)
        dialog.title("Add Stock")
        dialog.transient(self)
        dialog.grab_set()
        dialog.configure(bg=self.COLORS['background'])
        dialog.geometry("560x270")
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")

        # Create a frame with a border
        main_frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], highlightbackground=self.COLORS['border'], 
                             highlightthickness=1, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        entry_style = {"font": ("Helvetica", 10), "bg": self.COLORS['input_bg']}

        # Get available products
        try:
            conn = get_db_connection().__enter__()
            c = conn.cursor()
            c.execute("SELECT id, name FROM products ORDER BY name")
            products = c.fetchall()
            conn.close()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load products: {str(e)}")
            return

        if not products:
            messagebox.showerror("Error", "No products available")
            return

        tk.Label(main_frame, text="Product:", **label_style).grid(row=0, column=0, sticky="e", padx=(0, 10), pady=10)
        product_var = tk.StringVar()
        product_dropdown = ttk.Combobox(main_frame, textvariable=product_var,
                                      values=[f"{name} (ID: {id})" for id, name in products],
                                      state="readonly", width=40, font=("Helvetica", 10))
        product_dropdown.grid(row=0, column=1, padx=(0, 10), pady=10)

        tk.Label(main_frame, text="Quantity:", **label_style).grid(row=1, column=0, sticky="e", padx=(0, 10), pady=10)
        quantity_entry = tk.Entry(main_frame, width=40, **entry_style)
        quantity_entry.grid(row=1, column=1, padx=(0, 10), pady=10)

        def submit():
            selected = product_var.get()
            if not selected:
                messagebox.showerror("Error", "Please select a product")
                return

            try:
                quantity = int(quantity_entry.get())
                if quantity <= 0:
                    raise ValueError("Quantity must be positive")
            except ValueError:
                messagebox.showerror("Error", "Please enter a valid quantity")
                return

            product_id = int(selected.split("(ID: ")[1].rstrip(")"))

            try:
                conn = get_db_connection().__enter__()
                c = conn.cursor()
                
                # Update existing stock
                c.execute("UPDATE stock SET quantity = quantity + ? WHERE product_id = ?",
                         (quantity, product_id))
                
                conn.commit()
                messagebox.showinfo("Success", "Stock updated successfully")
                dialog.destroy()
                self._load_stock()
                # Refresh dashboard stock chart if available
                if 'dashboard' in self.nav:
                    self.nav['dashboard'].refresh_stock()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to update stock: {str(e)}")
            finally:
                conn.close()

        # Center the Save button
        button_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        button_frame.grid(row=2, columnspan=2, pady=20)
        tk.Button(button_frame, text="Save", command=submit, bg=self.COLORS['button_start'], 
                  fg="#FFFFFF", font=("Helvetica", 10, "bold"), relief=tk.FLAT, padx=20, pady=10,
                  activebackground=self.COLORS['button_end']).pack()

    def _delete_stock(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a stock item to delete")
            return

        stock_id = self.tree.item(selected[0])['values'][0]
        product_name = self.tree.item(selected[0])['values'][2]

        if not messagebox.askyesno("Confirm", f"Delete stock for '{product_name}'?"):
            return

        try:
            conn = get_db_connection().__enter__()
            c = conn.cursor()

            # Check if any items have been sold
            c.execute("SELECT sold FROM stock WHERE id = ?", (stock_id,))
            sold = c.fetchone()[0]

            if sold > 0:
                messagebox.showerror("Error", "Cannot delete stock - items have been sold")
                return

            c.execute("DELETE FROM stock WHERE id = ?", (stock_id,))
            conn.commit()
            messagebox.showinfo("Success", "Stock deleted successfully")
            self._load_stock()
            # Refresh dashboard stock chart if available
            if 'dashboard' in self.nav:
                self.nav['dashboard'].refresh_stock()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete stock: {str(e)}")
        finally:
            conn.close()

    def _delete_all_stock(self):
        if not messagebox.askyesno("Confirm", "Delete ALL stock records?"):
            return

        try:
            conn = get_db_connection().__enter__()
            c = conn.cursor()

            # Check if any items have been sold
            c.execute("SELECT SUM(sold) FROM stock")
            total_sold = c.fetchone()[0] or 0

            if total_sold > 0:
                messagebox.showerror("Error", "Cannot delete all stock - items have been sold")
                return

            c.execute("DELETE FROM stock")
            conn.commit()
            messagebox.showinfo("Success", "All stock records deleted")
            self._load_stock()
            # Refresh dashboard stock chart if available
            if 'dashboard' in self.nav:
                self.nav['dashboard'].refresh_stock()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete all stock: {str(e)}")
        finally:
            conn.close()

class Tooltip:
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip = None
        self.widget.bind("<Enter>", self.enter)
        self.widget.bind("<Leave>", self.leave)
        self.widget.bind("<ButtonPress>", self.leave)  # Close on click

    def enter(self, event=None):
        # Always show tooltip regardless of window state
        self.tooltip = tk.Toplevel(self.widget)
        self.tooltip.wm_overrideredirect(True)
        self.tooltip.wm_attributes("-topmost", True)  # Ensure it's on top
        
        label = tk.Label(
            self.tooltip,
            text=self.text,
            background="#4A6FA5",
            relief="solid",
            borderwidth=1,
            font=("Helvetica", 10),
            fg="#FFFFFF"
        )
        label.pack()
        
        # Get widget position relative to screen
        widget_x = self.widget.winfo_rootx()
        widget_y = self.widget.winfo_rooty()
        widget_width = self.widget.winfo_width()
        widget_height = self.widget.winfo_height()
        
        # Get tooltip dimensions
        self.tooltip.update_idletasks()  # Ensure size is calculated
        tooltip_width = self.tooltip.winfo_width()
        tooltip_height = self.tooltip.winfo_height()
        
        # Get screen dimensions
        screen_width = self.widget.winfo_screenwidth()
        screen_height = self.widget.winfo_screenheight()
        
        # Calculate position - below widget by default
        x = widget_x + (widget_width - tooltip_width) // 2
        y = widget_y + widget_height + 5
        
        # Adjust if tooltip would go off screen
        if x + tooltip_width > screen_width:
            x = screen_width - tooltip_width - 5
        elif x < 0:
            x = 5
            
        if y + tooltip_height > screen_height:
            y = widget_y - tooltip_height - 5
            if y < 0:
                y = 5
        
        self.tooltip.wm_geometry(f"+{int(x)}+{int(y)}")

    def leave(self, event=None):
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None
