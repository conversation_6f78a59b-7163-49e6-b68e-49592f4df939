"""
Database utilities for consistent connection management across the CRM application.
This module provides standardized database connection patterns to prevent locking issues.
"""

import sqlite3
import os
import logging
from contextlib import contextmanager

def get_db_path():
    """Get the standard database path"""
    appdata_path = os.getenv('APPDATA')
    if not appdata_path:
        raise EnvironmentError("APPDATA environment variable not found.")
    return os.path.join(appdata_path, 'CRM_System', 'crm_database.db')

@contextmanager
def get_db_connection(db_path=None, timeout=30):
    """
    Context manager for database connections with proper configuration.
    
    Usage:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM customers")
            results = cursor.fetchall()
    """
    if db_path is None:
        db_path = get_db_path()
    
    conn = None
    try:
        conn = sqlite3.connect(db_path, timeout=timeout)
        # Configure connection for optimal performance and concurrency
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA synchronous=NORMAL")
        conn.execute("PRAGMA busy_timeout=10000")  # 10 seconds
        conn.execute("PRAGMA temp_store=MEMORY")
        conn.execute("PRAGMA cache_size=10000")
        conn.execute("PRAGMA foreign_keys=ON")
        
        yield conn
        
    except Exception as e:
        if conn:
            conn.rollback()
        logging.error(f"Database error: {str(e)}")
        raise
    else:
        if conn:
            conn.commit()
    finally:
        if conn:
            conn.close()

def execute_query(query, params=None, fetch_one=False, fetch_all=True):
    """
    Execute a query with proper connection management.
    
    Args:
        query: SQL query string
        params: Query parameters (optional)
        fetch_one: Return only first result
        fetch_all: Return all results (default)
    
    Returns:
        Query results or None
    """
    with get_db_connection() as conn:
        cursor = conn.cursor()
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        if fetch_one:
            return cursor.fetchone()
        elif fetch_all:
            return cursor.fetchall()
        else:
            return cursor.lastrowid

def execute_transaction(operations):
    """
    Execute multiple operations in a single transaction.
    
    Args:
        operations: List of (query, params) tuples
    
    Returns:
        True if successful, raises exception on failure
    """
    with get_db_connection() as conn:
        cursor = conn.cursor()
        for query, params in operations:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
        return True

class DatabaseManager:
    """
    Singleton database manager to ensure consistent connection handling.
    """
    _instance = None
    _db_path = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabaseManager, cls).__new__(cls)
            cls._instance._db_path = get_db_path()
        return cls._instance
    
    def get_connection(self):
        """Get a database connection context manager"""
        return get_db_connection(self._db_path)
    
    def execute_query(self, query, params=None, fetch_one=False, fetch_all=True):
        """Execute a query using the manager"""
        return execute_query(query, params, fetch_one, fetch_all)
    
    def execute_transaction(self, operations):
        """Execute a transaction using the manager"""
        return execute_transaction(operations)

# Global database manager instance
db_manager = DatabaseManager()
