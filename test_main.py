import tkinter as tk
from login import LoginPage

def test_run():
    root = tk.Tk()
    root.title("CRM Test")
    root.geometry("1922x1080")
    
    # Test login page directly
    login = LoginPage(root, {
        'show_dashboard': lambda: print("Would show dashboard"),
        'show_register': lambda: print("Would show register"),
        'show_forgot_password': lambda: print("Would show forgot password")
    })
    login.pack(fill="both", expand=True)
    
    root.mainloop()

if __name__ == "__main__":
    test_run()