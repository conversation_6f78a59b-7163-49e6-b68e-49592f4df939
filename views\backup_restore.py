import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import shutil
import datetime
import zipfile
import sqlite3
import pandas as pd
from PIL import Image, ImageTk
import schedule
import time
import threading
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email.mime.text import MIMEText
from email import encoders
import logging
from google.auth.transport.requests import Request
import re
import inspect

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='backup_restore.log',
    filemode='a'
)

class BackupRestore(tk.Frame):
    COLORS = {
        'background': '#F8F9FA',        # Light gray background
        'card_bg': '#FFFFFF',           # White for cards
        'primary_accent': '#4A6FA5',    # Primary blue accent
        'secondary_accent': '#6C8FC7',  # Lighter blue for hover
        'text_primary': '#2D3748',      # Dark gray for primary text
        'text_secondary': '#718096',    # Lighter gray for secondary text
        'button_start': '#4A6FA5',      # Button default color
        'button_end': '#3A5A8C',        # Button hover/pressed color
        'border': '#E2E8F0',            # Light gray border
        'warning': '#E53E3E',           # Red for warnings/errors
        'input_bg': '#EDF2F7',          # Light gray for input fields
        'bar_colors': [
            '#4A6FA5',  # Primary accent
            '#6C8FC7',  # Secondary accent
            '#FFC107',  # Warning yellow
            '#E53E3E',  # Danger red
            '#2196F3',  # Info blue
            '#9E9E9E'   # Gray
        ],
        'pie_color1': '#4A6FA5',        # Primary accent for Paid
        'pie_color2': '#E53E3E',        # Danger red for Unpaid
        'pie_divider': '#FFFFFF',       # White
        'line_chart': '#4A6FA5',        # Primary accent
        'div_background': '#FFFFFF',    # White for div backgrounds
        'button_border': '#E2E8F0'      # Muted gray
    }

    SCOPES = ['https://www.googleapis.com/auth/drive.file']

    def __init__(self, parent, nav_commands):
        super().__init__(parent)
        from database import Database
from database_utils import get_db_connection
        self.nav_commands = nav_commands
        self.db = Database()
        logging.info(f"Loaded database module from: {inspect.getfile(Database)}")
        self.db_path = self.db.db_path
        self.configure(bg=self.COLORS['background'])
        self.google_drive_service = None
        self.icons = {}
        self.scheduled_jobs = []
        self.job_counter = 0
        self.backup_email_entry = None
        self.full_email_dropdown = None
        self.diff_email_dropdown = None
        self.scheduler_running = True
        
        self._create_ui()
        self._load_and_schedule_backups()
        self._start_schedule_thread()
        self.configure_styles()

    def _create_ui(self):
        main_container = tk.Frame(self, bg=self.COLORS['background'])
        main_container.pack(fill="both", expand=True)

        header_frame = tk.Frame(main_container, bg=self.COLORS['primary_accent'], height=90)
        header_frame.pack(fill="x", pady=(0, 20))

        self._create_header(header_frame)

        notebook = ttk.Notebook(main_container)
        notebook.pack(fill="both", expand=True, padx=10, pady=(0, 10))

        manual_tab = ttk.Frame(notebook)
        notebook.add(manual_tab, text="Manual Operations")
        self._create_manual_tab(manual_tab)

        schedule_tab = ttk.Frame(notebook)
        notebook.add(schedule_tab, text="Scheduled Backups")
        self._create_schedule_tab(schedule_tab)

        restore_tab = ttk.Frame(notebook)
        notebook.add(restore_tab, text="Restore")
        self._create_restore_tab(restore_tab)

        logs_tab = ttk.Frame(notebook)
        notebook.add(logs_tab, text="Backup Logs")
        self._create_logs_tab(logs_tab)

        error_logs_tab = ttk.Frame(notebook)
        notebook.add(error_logs_tab, text="Error Logs")
        self._create_error_logs_tab(error_logs_tab)

    def _create_header(self, header):
        def load_and_process_image(path):
            try:
                img = Image.open(path).convert("RGBA")
                data = img.getdata()
                new_data = []
                for item in data:
                    if item[3] > 0:
                        new_data.append((255, 255, 255, item[3]))
                    else:
                        new_data.append(item)
                img.putdata(new_data)
                img = img.resize((30, 30), Image.Resampling.LANCZOS)
                return ImageTk.PhotoImage(img)
            except Exception as e:
                logging.error(f"Error loading image: {str(e)}")
                img = Image.new('RGBA', (30, 30), (255, 255, 255, 0))
                return ImageTk.PhotoImage(img)

        try:
            self.dashboard_img = load_and_process_image(r"D:\CRM_System\assets\dashboard\home.png")
            dashboard_btn = tk.Button(
                header,
                image=self.dashboard_img,
                command=self.nav_commands['show_dashboard'],
                bg=self.COLORS['primary_accent'],
                relief=tk.FLAT,
                activebackground=self.COLORS['secondary_accent'],
                width=30, height=30
            )
            dashboard_btn.pack(side="left", padx=10)
            self._create_tooltip(dashboard_btn, "Dashboard")
        except Exception as e:
            logging.error(f"Error creating dashboard button: {str(e)}")

        header_label = tk.Label(
            header,
            text="Backup & Restore Manager",
            font=("Helvetica", 24, "bold"),
            fg="#FFFFFF",
            bg=self.COLORS['primary_accent']
        )
        header_label.place(relx=0.5, rely=0.5, anchor="center")

    def _create_tooltip(self, widget, text):
        def enter(event):
            self.tooltip = tk.Toplevel(widget)
            self.tooltip.wm_overrideredirect(True)
            label = tk.Label(
                self.tooltip,
                text=text,
                background=self.COLORS['primary_accent'],
                relief="solid",
                borderwidth=1,
                font=("Helvetica", 10),
                fg="#FFFFFF"
            )
            label.pack()

            widget_x = widget.winfo_rootx()
            widget_y = widget.winfo_rooty()
            widget_width = widget.winfo_width()
            tooltip_width = label.winfo_reqwidth()
            tooltip_height = label.winfo_reqheight()
            screen_width = self.winfo_screenwidth()
            screen_height = self.winfo_screenheight()

            x = widget_x + (widget_width - tooltip_width) // 2

            space_above = widget_y
            space_below = screen_height - (widget_y + widget.winfo_height())
            if space_above > space_below and space_above >= tooltip_height + 10:
                y = widget_y - tooltip_height - 10
            else:
                y = widget_y + widget.winfo_height() + 10

            if x < 0:
                x = 0
            elif x + tooltip_width > screen_width:
                x = screen_width - tooltip_width

            if y < 0:
                y = widget_y + widget.winfo_height() + 10
            elif y + tooltip_height > screen_height:
                y = widget_y - tooltip_height - 10

            self.tooltip.wm_geometry(f"+{x}+{y}")

        def leave(event):
            if hasattr(self, 'tooltip'):
                self.tooltip.destroy()

        widget.bind("<Enter>", enter)
        widget.bind("<Leave>", leave)

    def _create_manual_tab(self, manual_tab):
        email_frame = ttk.LabelFrame(manual_tab, text="Email Configuration", padding=10)
        email_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(email_frame, text="Sender Email:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.sender_email_label = ttk.Label(email_frame, text="")
        self.sender_email_label.grid(row=0, column=1, padx=5, pady=5, sticky="w")

        self._load_email_config()

        backup_frame = ttk.LabelFrame(manual_tab, text="Manual Backup Options", padding=10)
        backup_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(backup_frame, text="Backup Type:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.backup_type_var = tk.StringVar(value="full")
        ttk.Radiobutton(backup_frame, text="Full Backup", variable=self.backup_type_var, value="full").grid(row=0, column=1, padx=5, pady=5, sticky="w")
        ttk.Radiobutton(backup_frame, text="Differential Backup", variable=self.backup_type_var, value="differential").grid(row=0, column=2, padx=5, pady=5, sticky="w")

        ttk.Label(backup_frame, text="Destination:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.destination_var = tk.StringVar(value="local")
        ttk.Radiobutton(backup_frame, text="Local Storage", variable=self.destination_var, value="local").grid(row=1, column=1, padx=5, pady=5, sticky="w")
        ttk.Radiobutton(backup_frame, text="Email", variable=self.destination_var, value="email").grid(row=1, column=2, padx=5, pady=5, sticky="w")
        ttk.Radiobutton(backup_frame, text="Google Drive", variable=self.destination_var, value="gdrive").grid(row=1, column=3, padx=5, pady=5, sticky="w")

        self.local_path_frame = ttk.Frame(backup_frame)
        self.local_path_frame.grid(row=2, column=0, columnspan=4, padx=5, pady=5, sticky="we")
        
        ttk.Label(self.local_path_frame, text="Backup Path:").pack(side="left", padx=5)
        self.backup_path_entry = ttk.Entry(self.local_path_frame, width=40)
        self.backup_path_entry.pack(side="left", padx=5)
        ttk.Button(self.local_path_frame, text="Browse", command=self._browse_backup_path).pack(side="left")

        self.email_dest_frame = ttk.Frame(backup_frame)
        self.email_dest_frame.grid(row=2, column=0, columnspan=4, padx=5, pady=5, sticky="we")
        
        ttk.Label(self.email_dest_frame, text="Recipient Email:").pack(side="left", padx=5)
        self.backup_email_entry = ttk.Entry(self.email_dest_frame, width=40)
        self.backup_email_entry.pack(side="left", padx=5)

        self.email_dest_frame.grid_remove()
        self._update_backup_destination_ui()

        self.destination_var.trace_add("write", self._update_backup_destination_ui)

        ttk.Button(backup_frame, text="Run Backup Now", command=self._run_manual_backup, style="Accent.TButton").grid(row=3, column=0, columnspan=4, pady=10)

        export_frame = ttk.LabelFrame(manual_tab, text="Export Options", padding=10)
        export_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(export_frame, text="Export Path:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.export_path_entry = ttk.Entry(export_frame, width=40)
        self.after(100, lambda: self.export_path_entry.grid(row=0, column=1, padx=5, pady=5, sticky="we"))
        ttk.Button(export_frame, text="Browse", command=self._browse_export_path, width=10).grid(row=0, column=2, padx=5, pady=5)

        ttk.Label(export_frame, text="Format:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.export_format_var = tk.StringVar(value="csv")
        ttk.Radiobutton(export_frame, text="CSV", variable=self.export_format_var, value="csv").grid(row=1, column=1, padx=5, pady=5, sticky="w")
        ttk.Radiobutton(export_frame, text="Excel", variable=self.export_format_var, value="excel").grid(row=1, column=2, padx=5, pady=5, sticky="w")

        ttk.Button(export_frame, text="Export Now", command=self._run_export, style="Accent.TButton").grid(row=2, column=0, columnspan=3, pady=10)

    def _create_schedule_tab(self, schedule_tab):
        full_frame = ttk.LabelFrame(schedule_tab, text="Full Backup Schedule", padding=10)
        full_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(full_frame, text="Day:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.full_day_var = tk.StringVar(value="Daily")
        ttk.Combobox(full_frame, textvariable=self.full_day_var, values=[
            "Daily", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"
        ], state="readonly").grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(full_frame, text="Time:").grid(row=0, column=2, padx=5, pady=5, sticky="w")
        self.full_time_var = tk.StringVar(value="00:00")
        ttk.Entry(full_frame, textvariable=self.full_time_var, width=10).grid(row=0, column=3, padx=5, pady=5)

        ttk.Label(full_frame, text="Destination:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.full_dest_var = tk.StringVar(value="local")
        ttk.Radiobutton(full_frame, text="Local Storage", variable=self.full_dest_var, value="local").grid(row=1, column=1, padx=5, pady=5, sticky="w")
        ttk.Radiobutton(full_frame, text="Email", variable=self.full_dest_var, value="email").grid(row=1, column=2, padx=5, pady=5, sticky="w")
        ttk.Radiobutton(full_frame, text="Google Drive", variable=self.full_dest_var, value="gdrive").grid(row=1, column=3, padx=5, pady=5, sticky="w")

        self.full_local_frame = ttk.Frame(full_frame)
        self.full_local_frame.grid(row=2, column=0, columnspan=4, padx=5, pady=5, sticky="we")
        
        ttk.Label(self.full_local_frame, text="Backup Path:").pack(side="left", padx=5)
        self.full_path_entry = ttk.Entry(self.full_local_frame, width=40)
        self.full_path_entry.pack(side="left", padx=5)
        ttk.Button(self.full_local_frame, text="Browse", command=lambda: self._browse_schedule_path(self.full_path_entry)).pack(side="left")

        self.full_email_frame = ttk.Frame(full_frame)
        self.full_email_frame.grid(row=2, column=0, columnspan=4, padx=5, pady=5, sticky="we")
        
        ttk.Label(self.full_email_frame, text="Recipient Email:").pack(side="left", padx=5)
        self.full_email_dropdown = ttk.Combobox(self.full_email_frame, width=38)
        self.full_email_dropdown.pack(side="left", padx=5)

        self.full_email_frame.grid_remove()
        self._update_full_dest_ui()

        self.full_dest_var.trace_add("write", lambda *args: self._update_full_dest_ui())

        ttk.Button(full_frame, text="Schedule Full Backup", command=lambda: self._schedule_backup("full"), style="Accent.TButton").grid(row=3, column=0, columnspan=4, pady=10)

        diff_frame = ttk.LabelFrame(schedule_tab, text="Differential Backup Schedule", padding=10)
        diff_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(diff_frame, text="Time:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.diff_time_var = tk.StringVar(value="00:00")
        ttk.Entry(diff_frame, textvariable=self.diff_time_var, width=10).grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(diff_frame, text="Destination:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.diff_dest_var = tk.StringVar(value="local")
        ttk.Radiobutton(diff_frame, text="Local Storage", variable=self.diff_dest_var, value="local").grid(row=1, column=1, padx=5, pady=5, sticky="w")
        ttk.Radiobutton(diff_frame, text="Email", variable=self.diff_dest_var, value="email").grid(row=1, column=2, padx=5, pady=5, sticky="w")
        ttk.Radiobutton(diff_frame, text="Google Drive", variable=self.diff_dest_var, value="gdrive").grid(row=1, column=3, padx=5, pady=5, sticky="w")

        self.diff_local_frame = ttk.Frame(diff_frame)
        self.diff_local_frame.grid(row=2, column=0, columnspan=4, padx=5, pady=5, sticky="we")
        
        ttk.Label(self.diff_local_frame, text="Backup Path:").pack(side="left", padx=5)
        self.diff_path_entry = ttk.Entry(self.diff_local_frame, width=40)
        self.diff_path_entry.pack(side="left", padx=5)
        ttk.Button(self.diff_local_frame, text="Browse", command=lambda: self._browse_schedule_path(self.diff_path_entry)).pack(side="left")

        self.diff_email_frame = ttk.Frame(diff_frame)
        self.diff_email_frame.grid(row=2, column=0, columnspan=4, padx=5, pady=5, sticky="we")
        
        ttk.Label(self.diff_email_frame, text="Recipient Email:").pack(side="left", padx=5)
        self.diff_email_dropdown = ttk.Combobox(self.diff_email_frame, width=38)
        self.diff_email_dropdown.pack(side="left", padx=5)

        self.diff_email_frame.grid_remove()
        self._update_diff_dest_ui()

        self.diff_dest_var.trace_add("write", lambda *args: self._update_diff_dest_ui())

        ttk.Button(diff_frame, text="Schedule Differential Backup", command=lambda: self._schedule_backup("differential"), style="Accent.TButton").grid(row=3, column=0, columnspan=4, pady=10)

        self._update_email_dropdowns()

        title_frame = tk.Frame(schedule_tab, bg=self.COLORS['background'])
        title_frame.pack(fill="x", padx=10, pady=(5, 0))

        ttk.Label(
            title_frame,
            text="Scheduled Backups",
            font=("Helvetica", 12, "bold")
        ).pack(side="left")

        ttk.Button(
            title_frame,
            text="Delete Schedule",
            command=self._delete_selected_schedule,
            style="Accent.TButton"
        ).pack(side="right", padx=5)

        scheduled_frame = ttk.LabelFrame(schedule_tab, text="", padding=10)
        scheduled_frame.pack(fill="both", expand=True, padx=10, pady=5)

        columns = ("Type", "Schedule", "Destination", "Location")
        self.scheduled_list = ttk.Treeview(scheduled_frame, columns=columns, show="headings")
        for col in columns:
            self.scheduled_list.heading(col, text=col, anchor="center")
            self.scheduled_list.column(col, anchor="center")
        self.scheduled_list.heading("Type", text="Backup Type")
        self.scheduled_list.heading("Schedule", text="Schedule")
        self.scheduled_list.heading("Destination", text="Destination")
        self.scheduled_list.heading("Location", text="Location")
        self.scheduled_list.pack(fill="both", expand=True, padx=5, pady=5)

        button_frame = tk.Frame(scheduled_frame, bg=self.COLORS['background'])
        button_frame.pack(fill="x", pady=(10, 5))

        ttk.Button(button_frame, text="Remove Selected", command=self._remove_scheduled_backup, style="Accent.TButton").pack(side="left", padx=5)
        ttk.Button(button_frame, text="Delete Schedule", command=self._delete_selected_schedule, style="Accent.TButton").pack(side="left", padx=5)

    def _create_restore_tab(self, restore_tab):
        restore_frame = ttk.LabelFrame(restore_tab, text="Restore Options", padding=10)
        restore_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(restore_frame, text="Restore Type:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.restore_type_var = tk.StringVar(value="full")
        ttk.Radiobutton(restore_frame, text="Full Restore", variable=self.restore_type_var, value="full").grid(row=0, column=1, padx=5, pady=5, sticky="w")
        ttk.Radiobutton(restore_frame, text="Incremental Restore", variable=self.restore_type_var, value="incremental").grid(row=0, column=2, padx=5, pady=5, sticky="w")

        ttk.Label(restore_frame, text="Backup Files:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.restore_files_listbox = tk.Listbox(restore_frame, width=50, height=5)
        self.restore_files_listbox.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky="we")

        ttk.Button(restore_frame, text="Add Files", command=self._add_restore_files).grid(row=2, column=1, padx=5, pady=5)
        ttk.Button(restore_frame, text="Clear Files", command=lambda: self.restore_files_listbox.delete(0, tk.END)).grid(row=2, column=2, padx=5, pady=5)

        import_frame = ttk.LabelFrame(restore_tab, text="Import Data", padding=10)
        import_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(import_frame, text="Import Type:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.import_type_var = tk.StringVar(value="csv")
        ttk.Radiobutton(import_frame, text="CSV", variable=self.import_type_var, value="csv").grid(row=0, column=1, padx=5, pady=5, sticky="w")
        ttk.Radiobutton(import_frame, text="Excel", variable=self.import_type_var, value="excel").grid(row=0, column=2, padx=5, pady=5, sticky="w")

        ttk.Label(import_frame, text="Import Mode:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.import_mode_var = tk.StringVar(value="single")
        ttk.Radiobutton(import_frame, text="Single Table", variable=self.import_mode_var, value="single").grid(row=1, column=1, padx=5, pady=5, sticky="w")
        ttk.Radiobutton(import_frame, text="All Tables", variable=self.import_mode_var, value="all").grid(row=1, column=2, padx=5, pady=5, sticky="w")

        ttk.Label(import_frame, text="Import File:").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        self.import_file_entry = ttk.Entry(import_frame, width=40)
        self.import_file_entry.grid(row=2, column=1, padx=5, pady=5, sticky="we")
        ttk.Button(import_frame, text="Browse", command=self._browse_import_file).grid(row=2, column=2, padx=5, pady=5)

        ttk.Button(import_frame, text="Import Data", command=self._run_import, style="Accent.TButton").grid(row=3, column=0, columnspan=3, pady=10)

        ttk.Button(restore_frame, text="Restore Now", command=self._run_restore, style="Accent.TButton").grid(row=3, column=0, columnspan=3, pady=10)

    def _create_logs_tab(self, logs_tab):
        logs_frame = ttk.LabelFrame(logs_tab, text="Backup History", padding=10)
        logs_frame.pack(fill="both", expand=True, padx=10, pady=5)

        columns = ("Timestamp", "Type", "Destination", "Status", "Details")
        tree_frame = ttk.Frame(logs_frame)
        tree_frame.pack(fill="both", expand=True, padx=5, pady=5)

        self.logs_list = ttk.Treeview(tree_frame, columns=columns, show="headings")
        self.logs_list.heading("Timestamp", text="Timestamp")
        self.logs_list.heading("Type", text="Backup Type")
        self.logs_list.heading("Destination", text="Destination")
        self.logs_list.heading("Status", text="Status")
        self.logs_list.heading("Details", text="Details")
        self.logs_list.column("Timestamp", width=150)
        self.logs_list.column("Type", width=100)
        self.logs_list.column("Destination", width=100)
        self.logs_list.column("Status", width=80)
        self.logs_list.column("Details", width=300)

        yscroll = ttk.Scrollbar(tree_frame, orient="vertical", command=self.logs_list.yview)
        yscroll.pack(side="right", fill="y")
        self.logs_list.configure(yscrollcommand=yscroll.set)

        xscroll = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.logs_list.xview)
        xscroll.pack(side="bottom", fill="x")
        self.logs_list.configure(xscrollcommand=xscroll.set)

        self.logs_list.pack(fill="both", expand=True)

        refresh_button = ttk.Button(
            logs_frame,
            text="Refresh Logs",
            command=self._load_backup_logs,
            style="Accent.TButton"
        )
        refresh_button.pack(pady=5)

        self._load_backup_logs()

    def _create_error_logs_tab(self, error_logs_tab):
        error_logs_frame = ttk.LabelFrame(error_logs_tab, text="Error Log History", padding=10)
        error_logs_frame.pack(fill="both", expand=True, padx=10, pady=5)

        columns = ("Timestamp", "Level", "Message")
        tree_frame = ttk.Frame(error_logs_frame)
        tree_frame.pack(fill="both", expand=True, padx=5, pady=5)

        self.error_logs_list = ttk.Treeview(tree_frame, columns=columns, show="headings")
        self.error_logs_list.heading("Timestamp", text="Timestamp")
        self.error_logs_list.heading("Level", text="Log Level")
        self.error_logs_list.heading("Message", text="Message")
        self.error_logs_list.column("Timestamp", width=150)
        self.error_logs_list.column("Level", width=100)
        self.error_logs_list.column("Message", width=400)

        yscroll = ttk.Scrollbar(tree_frame, orient="vertical", command=self.error_logs_list.yview)
        yscroll.pack(side="right", fill="y")
        self.error_logs_list.configure(yscrollcommand=yscroll.set)

        xscroll = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.error_logs_list.xview)
        xscroll.pack(side="bottom", fill="x")
        self.error_logs_list.configure(xscrollcommand=xscroll.set)

        self.error_logs_list.pack(fill="both", expand=True)

        refresh_button = ttk.Button(
            error_logs_frame,
            text="Refresh Logs",
            command=self._load_error_logs,
            style="Accent.TButton"
        )
        refresh_button.pack(pady=5)

        self._load_error_logs()

    def _browse_import_file(self):
        import_type = self.import_type_var.get()
        import_mode = self.import_mode_var.get()
        
        if import_mode == "all":
            if import_type == "csv":
                path = filedialog.askdirectory(title="Select Folder Containing CSV Files")
                if path:
                    self.import_file_entry.delete(0, tk.END)
                    self.import_file_entry.insert(0, path)
            else:
                files = filedialog.askopenfilenames(
                    title="Select Excel Files",
                    filetypes=[("Excel Files", "*.xlsx"), ("All Files", "*.*")]
                )
                if files:
                    self.import_file_entry.delete(0, tk.END)
                    self.import_file_entry.insert(0, ";".join(files))
        else:
            if import_type == "csv":
                filetypes = [("CSV Files", "*.csv"), ("All Files", "*.*")]
            else:
                filetypes = [("Excel Files", "*.xlsx"), ("All Files", "*.*")]
                
            filename = filedialog.askopenfilename(title="Select Import File", filetypes=filetypes)
            if filename:
                self.import_file_entry.delete(0, tk.END)
                self.import_file_entry.insert(0, filename)

    def _run_import(self):
        import_file = self.import_file_entry.get()
        if not import_file:
            messagebox.showerror("Error", "Please select an import file/folder")
            return

        import_type = self.import_type_var.get()
        import_mode = self.import_mode_var.get()

        try:
            if import_mode == "single":
                tables = self._get_database_tables()
                if not tables:
                    messagebox.showerror("Error", "No tables found in database")
                    return

                dialog = tk.Toplevel(self)
                dialog.title("Select Table")
                dialog.transient(self)
                dialog.grab_set()
                dialog.configure(bg=self.COLORS['background'])
                dialog.geometry("300x200")
                
                tk.Label(dialog, text="Select Table to Import:", 
                        bg=self.COLORS['background'], fg=self.COLORS['text_primary']).pack(pady=10)
                
                table_var = tk.StringVar(value=tables[0])
                table_dropdown = ttk.Combobox(dialog, textvariable=table_var, values=tables, state="readonly")
                table_dropdown.pack(pady=10)
                
                def do_import():
                    table_name = table_var.get()
                    if not table_name:
                        messagebox.showerror("Error", "Please select a table")
                        return
                    
                    try:
                        if import_type == "csv":
                            df = pd.read_csv(import_file)
                            if not df.empty:  # Only proceed if there's data
                                self._import_csv_to_table(import_file, table_name)
                        else:
                            df = pd.read_excel(import_file, sheet_name=table_name)
                            if not df.empty:  # Only proceed if there's data
                                self._import_excel_to_table(import_file, table_name, sheet_name=table_name)
                        messagebox.showinfo("Success", f"Data imported to {table_name} successfully")
                        dialog.destroy()
                    except Exception as e:
                        messagebox.showerror("Error", f"Failed to import data: {str(e)}")
                
                import_btn = tk.Button(dialog, text="Import", command=do_import, 
                                     bg=self.COLORS['button_start'], fg="white",
                                     font=("Helvetica", 10, "bold"),
                                     relief=tk.FLAT,
                                     activebackground=self.COLORS['button_end'])
                import_btn.pack(pady=20)
                
            else:
                if import_type == "csv":
                    if not os.path.isdir(import_file):
                        messagebox.showerror("Error", "For CSV import of all tables, please select a directory containing CSV files")
                        return
                    
                    tables = self._get_database_tables()
                    imported = 0
                    for table in tables:
                        csv_file = os.path.join(import_file, f"{table}.csv")
                        if os.path.exists(csv_file):
                            try:
                                df = pd.read_csv(csv_file)
                                if not df.empty:  # Only proceed if there's data
                                    self._import_csv_to_table(csv_file, table)
                                    imported += 1
                            except Exception as e:
                                logging.error(f"Failed to import {table}: {str(e)}")
                    
                    messagebox.showinfo("Success", f"Imported data for {imported} tables")
                else:
                    excel_files = import_file.split(";")
                    if not excel_files:
                        messagebox.showerror("Error", "No Excel files selected")
                        return
                    
                    total_imported = 0
                    for excel_file in excel_files:
                        if not os.path.isfile(excel_file):
                            continue
                            
                        try:
                            xls = pd.ExcelFile(excel_file)
                            imported = 0
                            for sheet_name in xls.sheet_names:
                                if sheet_name in self._get_database_tables():
                                    try:
                                        df = pd.read_excel(excel_file, sheet_name=sheet_name)
                                        if not df.empty:  # Only proceed if there's data
                                            self._import_excel_to_table(excel_file, sheet_name, sheet_name=sheet_name)
                                            imported += 1
                                    except Exception as e:
                                        logging.error(f"Failed to import sheet {sheet_name}: {str(e)}")
                            total_imported += imported
                        except Exception as e:
                            logging.error(f"Failed to read Excel file {excel_file}: {str(e)}")
                    
                    messagebox.showinfo("Success", f"Imported data from {total_imported} sheets across {len(excel_files)} files")
                        
        except Exception as e:
            messagebox.showerror("Error", f"Import failed: {str(e)}")

    def _get_database_tables(self):
        conn = get_db_connection().__enter__()
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall() if row[0] != "sqlite_sequence"]
        conn.close()
        return tables

    def _import_csv_to_table(self, csv_file, table_name):
        df = pd.read_csv(csv_file)
        conn = get_db_connection().__enter__()
        
        cursor = conn.cursor()
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [column[1] for column in cursor.fetchall()]
        
        df = df[[col for col in df.columns if col in columns]]
        
        df.to_sql(table_name, conn, if_exists='replace', index=False)
        conn.commit()
        conn.close()

    def _import_excel_to_table(self, excel_file, table_name, sheet_name=None):
        try:
            if sheet_name is None:
                sheet_name = table_name
            
            # Read the Excel file to get sheet names
            xls = pd.ExcelFile(excel_file)
            
            # Try to find a matching sheet (case-insensitive)
            matching_sheet = None
            for actual_sheet in xls.sheet_names:
                if actual_sheet.lower() == sheet_name.lower():
                    matching_sheet = actual_sheet
                    break
            
            if matching_sheet:
                # If a matching sheet is found, read it
                df = pd.read_excel(excel_file, sheet_name=matching_sheet)
            else:
                # If no matching sheet is found, prompt the user to select one
                dialog = tk.Toplevel(self)
                dialog.title("Select Worksheet")
                dialog.transient(self)
                dialog.grab_set()
                dialog.configure(bg=self.COLORS['background'])
                dialog.geometry("300x200")
                
                tk.Label(dialog, text=f"No sheet named '{sheet_name}' found.\nPlease select a worksheet:", 
                        bg=self.COLORS['background'], fg=self.COLORS['text_primary']).pack(pady=10)
                
                sheet_var = tk.StringVar(value=xls.sheet_names[0] if xls.sheet_names else "")
                sheet_dropdown = ttk.Combobox(dialog, textvariable=sheet_var, values=xls.sheet_names, state="readonly")
                sheet_dropdown.pack(pady=10)
                
                def on_select():
                    selected_sheet = sheet_var.get()
                    dialog.selected_sheet = selected_sheet
                    dialog.destroy()
                
                import_btn = tk.Button(dialog, text="Select", command=on_select, 
                                     bg=self.COLORS['button_start'], fg="white",
                                     font=("Helvetica", 10, "bold"),
                                     relief=tk.FLAT,
                                     activebackground=self.COLORS['button_end'])
                import_btn.pack(pady=20)
                
                dialog.wait_window()  # Wait for the dialog to close
                if not hasattr(dialog, 'selected_sheet'):
                    raise ValueError("No worksheet selected")
                
                selected_sheet = dialog.selected_sheet
                df = pd.read_excel(excel_file, sheet_name=selected_sheet)

            # Get database table structure
            conn = get_db_connection().__enter__()
            cursor = conn.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns_info = cursor.fetchall()
            db_columns = [column[1] for column in columns_info]
            
            # Filter DataFrame to only include columns that exist in the database
            valid_columns = [col for col in df.columns if col in db_columns]
            if not valid_columns:
                raise ValueError(f"No matching columns found between Excel and database table {table_name}")
                
            df = df[valid_columns]
            
            # Convert empty strings to None for database compatibility
            df = df.replace(r'^\s*$', None, regex=True)
            
            # Handle single column case
            if len(valid_columns) == 1:
                # Remove rows where the single column is null
                df = df.dropna(subset=valid_columns)
            
            # Import data to SQLite
            df.to_sql(
                table_name, 
                conn, 
                if_exists='append',
                index=False
            )
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logging.error(f"Error importing to {table_name}: {str(e)}")
            if 'conn' in locals():
                conn.close()
            raise

    def _load_backup_logs(self):
        try:
            for item in self.logs_list.get_children():
                self.logs_list.delete(item)

            thirty_days_ago = (datetime.datetime.now() - datetime.timedelta(days=30)).strftime("%Y-%m-%d %H:%M:%S")
            conn = self.db.get_connection()
            try:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT timestamp, type, location, status, 
                           COALESCE(error_message, '') as details
                    FROM backup_history
                    WHERE timestamp >= ?
                    ORDER BY timestamp DESC
                """, (thirty_days_ago,))
                logs = cursor.fetchall()
            finally:
                self.db.close_connection()

            for log in logs:
                timestamp, backup_type, location, status, details = log
                self.logs_list.insert("", "end", values=(
                    timestamp,
                    backup_type,
                    location.capitalize() if location in ["local", "email", "gdrive"] else location,
                    status.capitalize(),
                    details
                ))
        except Exception as e:
            logging.error(f"Error loading backup logs: {str(e)}", exc_info=True)
            messagebox.showerror("Error", f"Failed to load backup logs: {str(e)}")

    def _load_error_logs(self):
        try:
            for item in self.error_logs_list.get_children():
                self.error_logs_list.delete(item)

            thirty_days_ago = (datetime.datetime.now() - datetime.timedelta(days=30)).strftime("%Y-%m-%d %H:%M:%S")
            conn = self.db.get_connection()
            try:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT timestamp, status, error_message
                    FROM backup_history
                    WHERE status = 'failed'
                    AND timestamp >= ?
                    ORDER BY timestamp DESC
                """, (thirty_days_ago,))
                logs = cursor.fetchall()
            finally:
                self.db.close_connection()

            for log in logs:
                timestamp, status, error_message = log
                if error_message:
                    self.error_logs_list.insert("", "end", values=(
                        timestamp,
                        "ERROR",
                        error_message
                    ))

            if not self.error_logs_list.get_children():
                logging.info("No failed backups with error messages found in backup_history")

        except Exception as e:
            logging.error(f"Error loading error logs from backup_history: {str(e)}", exc_info=True)
            messagebox.showerror("Error", f"Failed to load error logs: {str(e)}")

    def _delete_selected_schedule(self):
        selection = self.scheduled_list.selection()
        if not selection:
            messagebox.showerror("Error", "Please select a schedule to delete")
            return
            
        job_id = selection[0]
        
        schedule.clear(job_id)
        self.db.remove_scheduled_backup(int(job_id))
        self.scheduled_list.delete(job_id)
        
        messagebox.showinfo("Success", "Schedule deleted successfully")

    def _load_email_config(self):
        try:
            config = self.db.get_email_config()
            logging.debug(f"Email config: {config}")
            if config and len(config) >= 5:
                if isinstance(config[1], str):
                    self.sender_email_label.configure(text=config[1])
                else:
                    logging.warning(f"Invalid email in config[1]: {config[1]}")
            else:
                logging.warning(f"Invalid or incomplete config: {config}")
                self.sender_email_label.configure(text="Not configured")
        except Exception as e:
            logging.error(f"Error loading email configuration: {str(e)}")
            messagebox.showwarning("Warning", f"Failed to load email configuration: {str(e)}")

    def _update_backup_destination_ui(self, *args):
        destination = self.destination_var.get()
        
        if destination == "email":
            self.local_path_frame.grid_remove()
            self.email_dest_frame.grid()
            try:
                config = self.db.get_email_config()
                if config and config[1]:
                    self.backup_email_entry.delete(0, tk.END)
                    self.backup_email_entry.insert(0, config[1])
                else:
                    self.backup_email_entry.delete(0, tk.END)
                    self.backup_email_entry.insert(0, "")
            except Exception as e:
                logging.error(f"Error fetching email for manual backup: {str(e)}")
                self.backup_email_entry.delete(0, tk.END)
                self.backup_email_entry.insert(0, "")
        else:
            self.email_dest_frame.grid_remove()
            self.local_path_frame.grid()

    def _update_email_dropdowns(self):
        logging.info("Updating email dropdowns")
        try:
            config = self.db.get_email_config()
            emails = [config[1]] if config and config[1] else []
            
            if hasattr(self, 'full_email_dropdown') and self.full_email_dropdown:
                self.full_email_dropdown['values'] = emails
                if emails:
                    self.full_email_dropdown.set(emails[0])
                else:
                    self.full_email_dropdown.set("")

            if hasattr(self, 'diff_email_dropdown') and self.diff_email_dropdown:
                self.diff_email_dropdown['values'] = emails
                if emails:
                    self.diff_email_dropdown.set(emails[0])
                else:
                    self.diff_email_dropdown.set("")

        except Exception as e:
            logging.error(f"Error updating email dropdowns: {str(e)}")
            if hasattr(self, 'full_email_dropdown') and self.full_email_dropdown:
                self.full_email_dropdown.set("")
            if hasattr(self, 'diff_email_dropdown') and self.diff_email_dropdown:
                self.diff_email_dropdown.set("")

    def _update_full_dest_ui(self, *args):
        destination = self.full_dest_var.get()
        
        if destination == "email":
            self.full_local_frame.grid_remove()
            self.full_email_frame.grid()
            try:
                config = self.db.get_email_config()
                emails = [config[1]] if config and config[1] else []
                if hasattr(self, 'full_email_dropdown') and self.full_email_dropdown:
                    self.full_email_dropdown['values'] = emails
                    if emails:
                        self.full_email_dropdown.set(emails[0])
                    else:
                        self.full_email_dropdown.set("")
            except Exception as e:
                logging.error(f"Error fetching email for full backup schedule: {str(e)}")
                if hasattr(self, 'full_email_dropdown') and self.full_email_dropdown:
                    self.full_email_dropdown.set("")
        elif destination == "gdrive":
            self.full_local_frame.grid_remove()
            self.full_email_frame.grid_remove()
        else:
            self.full_email_frame.grid_remove()
            self.full_local_frame.grid()

    def _update_diff_dest_ui(self, *args):
        destination = self.diff_dest_var.get()
        
        if destination == "email":
            self.diff_local_frame.grid_remove()
            self.diff_email_frame.grid()
            try:
                config = self.db.get_email_config()
                emails = [config[1]] if config and config[1] else []
                if hasattr(self, 'diff_email_dropdown') and self.diff_email_dropdown:
                    self.diff_email_dropdown['values'] = emails
                    if emails:
                        self.diff_email_dropdown.set(emails[0])
                    else:
                        self.diff_email_dropdown.set("")
            except Exception as e:
                logging.error(f"Error fetching email for differential backup schedule: {str(e)}")
                if hasattr(self, 'diff_email_dropdown') and self.diff_email_dropdown:
                    self.diff_email_dropdown.set("")
        elif destination == "gdrive":
            self.diff_local_frame.grid_remove()
            self.diff_email_frame.grid_remove()
        else:
            self.diff_email_frame.grid_remove()
            self.diff_local_frame.grid()

    def _validate_email_config(self, smtp_email, smtp_password):
        if not all([smtp_email, smtp_password]):
            return False
        
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, smtp_email):
            return False
            
        if not smtp_password.strip():
            return False
        return True

    def _browse_backup_path(self):
        path = filedialog.askdirectory()
        if path:
            self.backup_path_entry.delete(0, tk.END)
            self.backup_path_entry.insert(0, path)

    def _browse_export_path(self):
        path = filedialog.askdirectory()
        if path:
            self.export_path_entry.delete(0, tk.END)
            self.export_path_entry.insert(0, path)

    def _browse_schedule_path(self, entry_widget):
        path = filedialog.askdirectory()
        if path:
            entry_widget.delete(0, tk.END)
            entry_widget.insert(0, path)

    def _add_restore_files(self):
        files = filedialog.askopenfilenames(
            title="Select Backup Files",
            filetypes=[("Database files", "*.db *.bak *.zip"), ("All files", "*.*")]
        )
        for file in files:
            self.restore_files_listbox.insert(tk.END, file)

    def _run_manual_backup(self):
        backup_type = self.backup_type_var.get()
        destination = self.destination_var.get()

        if destination == "local" or destination == "gdrive":
            path = self.backup_path_entry.get()
            if not path:
                messagebox.showerror("Error", "Please select a backup directory")
                return
        elif destination == "email":
            config = self.db.get_email_config()
            if not config or not config[1]:
                messagebox.showerror("Error", "Email not configured. Please configure email settings first.")
                return
            email = self.backup_email_entry.get()
            if not email:
                messagebox.showerror("Error", "Please enter a recipient email address.")
                return

        try:
            if backup_type == "full":
                if destination == "local":
                    success = self._backup_database_local()
                elif destination == "email":
                    success = self._backup_to_email(email)
                elif destination == "gdrive":
                    success = self._backup_to_google_drive()
            else:
                if destination == "local":
                    success = self._backup_database_differential()
                elif destination == "email":
                    success = self._backup_database_differential_to_email(email)
                elif destination == "gdrive":
                    success = self._backup_database_differential_to_gdrive()

            if success:
                messagebox.showinfo("Success", "Backup completed successfully!")
            else:
                messagebox.showerror("Error", "Backup failed")
        except Exception as e:
            messagebox.showerror("Error", f"Backup failed: {str(e)}")
            self.db.record_backup_history(
                f"{backup_type.capitalize()} Backup",
                destination,
                status="failed",
                error_message=str(e)
            )

    def _run_export(self):
        export_path = self.export_path_entry.get()
        export_format = self.export_format_var.get()

        if not export_path:
            messagebox.showerror("Error", "Please select an export directory")
            return

        try:
            if export_format == "csv":
                success = self._export_to_csv()
            else:
                success = self._export_to_excel()

            if success:
                messagebox.showinfo("Success", "Export completed successfully!")
            else:
                messagebox.showerror("Error", "Export failed")
        except Exception as e:
            messagebox.showerror("Error", f"Export failed: {str(e)}")

    def _run_restore(self):
        restore_type = self.restore_type_var.get()
        files = self.restore_files_listbox.get(0, tk.END)

        if not files:
            messagebox.showerror("Error", "Please select at least one backup file")
            return

        try:
            if restore_type == "full":
                if len(files) != 1:
                    messagebox.showerror("Error", "Full restore requires exactly one backup file")
                    return
                success = self._restore_database(files[0])
            else:
                if len(files) < 2:
                    messagebox.showerror("Error", "Incremental restore requires at least two files (one full + differentials)")
                    return
                success = self._restore_incremental(files)

            if success:
                messagebox.showinfo("Success", "Restore completed successfully!")
            else:
                messagebox.showerror("Error", "Restore failed")
        except Exception as e:
            messagebox.showerror("Error", f"Restore failed: {str(e)}")

    def _schedule_backup(self, backup_type):
        try:
            if backup_type == "full":
                day = self.full_day_var.get()
                time_str = self.full_time_var.get()
                destination = self.full_dest_var.get()
                path = self.full_path_entry.get()
                email = self.full_email_dropdown.get() if destination == "email" else None
            else:
                day = "daily"
                time_str = self.diff_time_var.get()
                destination = self.diff_dest_var.get()
                path = self.diff_path_entry.get()
                email = self.diff_email_dropdown.get() if destination == "email" else None

            if destination == "local" and not path:
                default_path = os.path.join(os.path.dirname(self.db_path), "backups")
                path = default_path
                if backup_type == "full":
                    self.full_path_entry.delete(0, tk.END)
                    self.full_path_entry.insert(0, path)
                else:
                    self.diff_path_entry.delete(0, tk.END)
                    self.diff_path_entry.insert(0, path)

            if destination == "email" and not email:
                messagebox.showerror("Error", "Please select an email address")
                return

            job_id = self.job_counter
            self.job_counter += 1

            self.db.save_scheduled_backup(
                job_id, backup_type, day, time_str, path, destination, email
            )

            if backup_type == "full":
                if day.lower() == "daily":
                    schedule.every().day.at(time_str).do(
                        self._execute_scheduled_backup, job_id, backup_type, destination, path, email
                    ).tag(job_id)
                else:
                    getattr(schedule.every(), day.lower()).at(time_str).do(
                        self._execute_scheduled_backup, job_id, backup_type, destination, path, email
                    ).tag(job_id)
            else:
                schedule.every().day.at(time_str).do(
                    self._execute_scheduled_backup, job_id, backup_type, destination, path, email
                ).tag(job_id)

            self.scheduled_list.insert("", "end", iid=str(job_id), values=(
                backup_type.capitalize(),
                f"{day} at {time_str}" if backup_type == "full" else f"Daily at {time_str}",
                destination.capitalize(),
                path if destination == "local" else (email if destination == "email" else "Google Drive")
            ))

            messagebox.showinfo("Success", f"{backup_type.capitalize()} backup scheduled successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to schedule backup: {str(e)}")
            logging.error(f"Error scheduling backup: {str(e)}")

    def _execute_scheduled_backup(self, job_id, backup_type, destination, path, email=None):
        try:
            logging.info(f"Starting scheduled {backup_type} backup (ID: {job_id})")
            
            self.db.close_connection()
            
            if backup_type == "full":
                if destination == "local":
                    success = self._backup_database_local(path)
                elif destination == "email":
                    success = self._backup_to_email(email)
                elif destination == "gdrive":
                    success = self._backup_to_google_drive()
            else:
                if destination == "local":
                    success = self._backup_database_differential(path)
                elif destination == "email":
                    success = self._backup_database_differential_to_email(email)
                elif destination == "gdrive":
                    success = self._backup_database_differential_to_gdrive()

            if success:
                self.db.record_backup_history(
                    f"Scheduled {backup_type.capitalize()} Backup",
                    destination,
                    status="success"
                )
                logging.info(f"Successfully executed scheduled backup {job_id}")
            else:
                self.db.record_backup_history(
                    f"Scheduled {backup_type.capitalize()} Backup",
                    destination,
                    status="failed",
                    error_message="Unknown error"
                )
                logging.error(f"Failed to execute scheduled backup {job_id}")
        except Exception as e:
            logging.error(f"Failed to execute scheduled backup {job_id}: {str(e)}")
            self.db.record_backup_history(
                f"Scheduled {backup_type.capitalize()} Backup",
                destination,
                status="failed",
                error_message=str(e)
            )
        finally:
            self.db.close_connection()

    def _remove_scheduled_backup(self):
        selection = self.scheduled_list.selection()
        if not selection:
            messagebox.showerror("Error", "Please select a backup to remove")
            return

        job_id = selection[0]
        schedule.clear(job_id)
        self.db.remove_scheduled_backup(int(job_id))
        self.scheduled_list.delete(job_id)
        messagebox.showinfo("Success", "Scheduled backup removed")

    def _backup_database_local(self, path=None):
        path = path or self.backup_path_entry.get()
        if not path:
            raise ValueError("Backup path not specified")

        if not os.path.exists(path):
            os.makedirs(path)

        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(path, f"crm_backup_{timestamp}.bak")

        self.db.close_connection()
        success = self.db.backup_database(backup_file)
        if not success:
            raise Exception("Database backup failed")

        zip_file = os.path.join(path, f"crm_backup_{timestamp}.zip")
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            zipf.write(backup_file, os.path.basename(backup_file))

        self.db.record_backup_history("Local Backup", backup_file)
        return True

    def _backup_database_differential(self, path=None):
        path = path or self.backup_path_entry.get()
        if not path:
            raise ValueError("Backup path not specified")

        if not os.path.exists(path):
            os.makedirs(path)

        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(path, f"crm_diff_backup_{timestamp}.bak")

        self.db.close_connection()
        success = self.db.differential_backup(backup_file)
        if not success:
            raise Exception("Differential backup failed")

        zip_file = os.path.join(path, f"crm_diff_backup_{timestamp}.zip")
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            zipf.write(backup_file, os.path.basename(backup_file))

        self.db.record_backup_history("Differential Backup", backup_file)
        return True

    def _backup_database_differential_to_email(self, email):
        try:
            config = self.db.get_email_config()
            if not config or not config[1]:
                raise ValueError("Email not configured")

            smtp_email, smtp_password = config[1], config[2]
            smtp_server = config[3]
            smtp_port = config[4]

            if not self._validate_email_config(smtp_email, smtp_password):
                raise ValueError("Invalid email configuration")

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_backup = os.path.join(os.path.dirname(self.db_path), f"temp_diff_backup_{timestamp}.bak")

            self.db.close_connection()
            if not self.db.differential_backup(temp_backup):
                raise Exception("Differential backup failed")

            zip_file = os.path.join(os.path.dirname(self.db_path), f"crm_diff_backup_{timestamp}.zip")
            with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(temp_backup, os.path.basename(temp_backup))

            msg = MIMEMultipart()
            msg['From'] = smtp_email
            msg['To'] = email
            msg['Subject'] = f"CRM Differential Backup {timestamp}"

            with open(zip_file, "rb") as f:
                part = MIMEBase("application", "octet-stream")
                part.set_payload(f.read())
            encoders.encode_base64(part)
            part.add_header("Content-Disposition", f"attachment; filename={os.path.basename(zip_file)}")
            msg.attach(part)

            with smtplib.SMTP_SSL(smtp_server, smtp_port) as server:
                server.login(smtp_email, smtp_password)
                server.sendmail(smtp_email, email, msg.as_string())

            try:
                if os.path.exists(temp_backup):
                    os.remove(temp_backup)
                if os.path.exists(zip_file):
                    os.remove(zip_file)
            except Exception as clean_error:
                logging.warning(f"Error cleaning up temporary files: {str(clean_error)}")

            return True
        except Exception as e:
            try:
                if 'temp_backup' in locals() and os.path.exists(temp_backup):
                    os.remove(temp_backup)
                if 'zip_file' in locals() and os.path.exists(zip_file):
                    os.remove(zip_file)
            except:
                pass
            raise

    def _backup_database_differential_to_gdrive(self):
        if not self._authenticate_google_drive():
            raise Exception("Google Drive authentication failed")

        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        temp_backup = os.path.join(os.path.dirname(self.db_path), f"temp_diff_backup_{timestamp}.bak")

        self.db.close_connection()
        if not self.db.differential_backup(temp_backup):
            raise Exception("Differential backup failed")

        zip_file = os.path.join(os.path.dirname(self.db_path), f"crm_diff_backup_{timestamp}.zip")
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            zipf.write(temp_backup, os.path.basename(temp_backup))

        file_metadata = {'name': os.path.basename(zip_file)}
        media = MediaFileUpload(zip_file, mimetype='application/zip')
        file = self.google_drive_service.files().create(
            body=file_metadata,
            media_body=media,
            fields='id'
        ).execute()

        os.remove(temp_backup)
        os.remove(zip_file)
        
        return True

    def _backup_to_email(self, email):
        try:
            config = self.db.get_email_config()
            if not config or not config[1]:
                raise ValueError("Email not configured")

            smtp_email, smtp_password = config[1], config[2]
            smtp_server = config[3]
            smtp_port = config[4]

            if not self._validate_email_config(smtp_email, smtp_password):
                raise ValueError("Invalid email configuration")

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_backup = os.path.join(os.path.dirname(self.db_path), f"temp_backup_{timestamp}.bak")

            self.db.close_connection()
            if not self.db.backup_database(temp_backup):
                raise Exception("Database backup failed")

            zip_file = os.path.join(os.path.dirname(self.db_path), f"crm_backup_{timestamp}.zip")
            with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(temp_backup, os.path.basename(temp_backup))

            msg = MIMEMultipart()
            msg['From'] = smtp_email
            msg['To'] = email
            msg['Subject'] = f"CRM Backup {timestamp}"

            with open(zip_file, "rb") as f:
                part = MIMEBase("application", "octet-stream")
                part.set_payload(f.read())
            encoders.encode_base64(part)
            part.add_header("Content-Disposition", f"attachment; filename={os.path.basename(zip_file)}")
            msg.attach(part)

            with smtplib.SMTP_SSL(smtp_server, smtp_port) as server:
                server.login(smtp_email, smtp_password)
                server.sendmail(smtp_email, email, msg.as_string())

            try:
                if os.path.exists(temp_backup):
                    os.remove(temp_backup)
                if os.path.exists(zip_file):
                    os.remove(zip_file)
            except Exception as clean_error:
                logging.warning(f"Error cleaning up temporary files: {str(clean_error)}")

            self.db.record_backup_history("Email Backup", f"Sent to {email}")
            return True
        except Exception as e:
            try:
                if 'temp_backup' in locals() and os.path.exists(temp_backup):
                    os.remove(temp_backup)
                if 'zip_file' in locals() and os.path.exists(zip_file):
                    os.remove(zip_file)
            except:
                pass
            raise

    def _backup_to_google_drive(self):
        if not self._authenticate_google_drive():
            raise Exception("Google Drive authentication failed")

        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        temp_backup = os.path.join(os.path.dirname(self.db_path), f"temp_backup_{timestamp}.bak")

        self.db.close_connection()
        if not self.db.backup_database(temp_backup):
            raise Exception("Database backup failed")

        zip_file = os.path.join(os.path.dirname(self.db_path), f"crm_backup_{timestamp}.zip")
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            zipf.write(temp_backup, os.path.basename(temp_backup))

        file_metadata = {'name': os.path.basename(zip_file)}
        media = MediaFileUpload(zip_file, mimetype='application/zip')
        file = self.google_drive_service.files().create(
            body=file_metadata,
            media_body=media,
            fields='id'
        ).execute()

        os.remove(temp_backup)
        os.remove(zip_file)
        
        self.db.record_backup_history("Google Drive Backup", f"File ID: {file.get('id')}")
        return True

    def _export_to_csv(self):
        export_path = self.export_path_entry.get()
        if not export_path:
            raise ValueError("Export path not specified")

        if not os.path.exists(export_path):
            os.makedirs(export_path)

        self.db.close_connection()
        return self.db.export_to_csv(export_path)

    def _export_to_excel(self):
        export_path = self.export_path_entry.get()
        if not export_path:
            raise ValueError("Export path not specified")

        if not os.path.exists(export_path):
            os.makedirs(export_path)

        self.db.close_connection()
        return self.db.export_to_excel(export_path)

    def _restore_database(self, backup_file):
        if not os.path.exists(backup_file):
            raise ValueError("Backup file not found")

        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        pre_restore_backup = os.path.join(os.path.dirname(self.db_path), f"pre_restore_{timestamp}.bak")
        self.db.close_connection()
        self.db.backup_database(pre_restore_backup)

        if backup_file.endswith('.zip'):
            temp_dir = os.path.join(os.path.dirname(self.db_path), "temp_restore")
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            with zipfile.ZipFile(backup_file, 'r') as zipf:
                zipf.extractall(temp_dir)

            db_files = [f for f in os.listdir(temp_dir) if f.endswith(('.db', '.bak'))]
            if not db_files:
                shutil.rmtree(temp_dir)
                raise ValueError("No database file found in zip archive")

            restore_file = os.path.join(temp_dir, db_files[0])
            success = self.db.restore_database(restore_file)
            shutil.rmtree(temp_dir)
        else:
            success = self.db.restore_database(backup_file)

        return success

    def _restore_incremental(self, backup_files):
        full_backup = backup_files[0]
        differentials = backup_files[1:]

        if not self._restore_database(full_backup):
            return False

        for diff in differentials:
            if not self.db.apply_differential_backup(diff):
                return False

        return True

    def _authenticate_google_drive(self):
        if self.google_drive_service:
            try:
                self.google_drive_service.files().list(pageSize=1).execute()
                return True
            except:
                self.google_drive_service = None

        config = self.db.get_google_drive_config()
        if not config or len(config) < 4:
            messagebox.showerror("Error", "Google Drive not configured. Please set up Google Drive in the database.")
            return False

        client_id, client_secret, token_path, is_configured = config[1:5]
        if not is_configured:
            messagebox.showerror("Error", "Google Drive configuration is incomplete.")
            return False

        creds = None
        if os.path.exists(token_path):
            creds = Credentials.from_authorized_user_file(token_path, self.SCOPES)

        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                creds_path = os.path.join(os.path.dirname(token_path), "credentials.json")
                if not os.path.exists(creds_path):
                    messagebox.showerror("Error", "Google Drive credentials file missing.")
                    return False
                flow = InstalledAppFlow.from_client_secrets_file(creds_path, self.SCOPES)
                creds = flow.run_local_server(port=0)
                with open(token_path, 'w') as token_file:
                    token_file.write(creds.to_json())

        self.google_drive_service = build('drive', 'v3', credentials=creds)
        return True

    def _load_and_schedule_backups(self):
        try:
            scheduled_backups = self.db.get_scheduled_backups()
            for backup in scheduled_backups:
                job_id, backup_type, day, time_str, path, destination, email = backup
                self.job_counter = max(self.job_counter, job_id + 1)

                self.scheduled_list.insert("", "end", iid=str(job_id), values=(
                    backup_type.capitalize(),
                    f"{day} at {time_str}" if backup_type == "full" else f"Daily at {time_str}",
                    destination.capitalize(),
                    path if destination == "local" else (email if destination == "email" else "Google Drive")
                ))

                if backup_type == "full":
                    if day.lower() == "daily":
                        schedule.every().day.at(time_str).do(
                            self._execute_scheduled_backup, job_id, backup_type, destination, path, email
                        ).tag(job_id)
                    else:
                        getattr(schedule.every(), day.lower()).at(time_str).do(
                            self._execute_scheduled_backup, job_id, backup_type, destination, path, email
                        ).tag(job_id)
                else:
                    schedule.every().day.at(time_str).do(
                        self._execute_scheduled_backup, job_id, backup_type, destination, path, email
                    ).tag(job_id)
        except Exception as e:
            logging.error(f"Error loading scheduled backups: {str(e)}")

    def _start_schedule_thread(self):
        def run_schedule():
            logging.info("Scheduler thread started")
            while getattr(self, 'scheduler_running', True):
                try:
                    schedule.run_pending()
                    time.sleep(60)
                except Exception as e:
                    logging.error(f"Error in scheduler thread: {str(e)}")
                    time.sleep(5)

        thread = threading.Thread(target=run_schedule, daemon=True)
        thread.start()

    def configure_styles(self):
        style = ttk.Style()
        style.configure("Accent.TButton", 
                      background=self.COLORS['button_start'], 
                      foreground="Black",
                      font=("Helvetica", 10, "bold"))
        style.map("Accent.TButton",
                background=[('active', self.COLORS['button_end'])],
                foreground=[('active', 'white')])

    def destroy(self):
        self.scheduler_running = False
        if hasattr(self, '_scheduler_thread') and self._scheduler_thread.is_alive():
            self._scheduler_thread.join(timeout=1)
        super().destroy()
