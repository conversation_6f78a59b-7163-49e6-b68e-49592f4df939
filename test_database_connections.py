#!/usr/bin/env python3
"""
Test script to verify database connections are working properly
and no locks are occurring.
"""

import os
import sys
import time
import threading
import sqlite3
from database_utils import get_db_connection, db_manager

def test_basic_connection():
    """Test basic database connection"""
    print("Testing basic database connection...")
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM customers")
            count = cursor.fetchone()[0]
            print(f"✓ Basic connection successful. Found {count} customers.")
            return True
    except Exception as e:
        print(f"✗ Basic connection failed: {e}")
        return False

def test_concurrent_reads():
    """Test concurrent read operations"""
    print("Testing concurrent read operations...")
    
    def read_customers():
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM customers")
                return cursor.fetchone()[0]
        except Exception as e:
            print(f"Read error: {e}")
            return None
    
    # Start multiple threads doing reads
    threads = []
    results = []
    
    def worker():
        result = read_customers()
        results.append(result)
    
    for i in range(5):
        thread = threading.Thread(target=worker)
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()
    
    if all(r is not None for r in results):
        print(f"✓ Concurrent reads successful. All threads returned results.")
        return True
    else:
        print(f"✗ Concurrent reads failed. Some threads failed.")
        return False

def test_read_write_operations():
    """Test mixed read/write operations"""
    print("Testing mixed read/write operations...")
    
    try:
        # Test transaction
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # Read current count
            cursor.execute("SELECT COUNT(*) FROM regions")
            initial_count = cursor.fetchone()[0]
            
            # Try to insert a test region
            test_region = f"Test_Region_{int(time.time())}"
            cursor.execute("INSERT INTO regions (name) VALUES (?)", (test_region,))
            
            # Read new count
            cursor.execute("SELECT COUNT(*) FROM regions")
            new_count = cursor.fetchone()[0]
            
            # Clean up - delete the test region
            cursor.execute("DELETE FROM regions WHERE name = ?", (test_region,))
            
            if new_count == initial_count + 1:
                print("✓ Read/write operations successful.")
                return True
            else:
                print("✗ Read/write operations failed.")
                return False
                
    except Exception as e:
        print(f"✗ Read/write test failed: {e}")
        return False

def test_database_lock_detection():
    """Test for potential database locks"""
    print("Testing for database lock issues...")
    
    def long_running_operation():
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                # Simulate a longer operation
                cursor.execute("SELECT * FROM customers")
                customers = cursor.fetchall()
                time.sleep(0.1)  # Small delay
                cursor.execute("SELECT * FROM packages")
                packages = cursor.fetchall()
                return len(customers), len(packages)
        except Exception as e:
            print(f"Long operation error: {e}")
            return None, None
    
    # Run multiple operations simultaneously
    threads = []
    results = []
    
    def worker():
        result = long_running_operation()
        results.append(result)
    
    for i in range(3):
        thread = threading.Thread(target=worker)
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()
    
    if all(r[0] is not None for r in results):
        print("✓ No database locks detected.")
        return True
    else:
        print("✗ Potential database locks detected.")
        return False

def test_database_manager():
    """Test the database manager singleton"""
    print("Testing database manager...")
    
    try:
        # Test singleton behavior
        manager1 = db_manager
        manager2 = db_manager
        
        if manager1 is manager2:
            print("✓ Database manager singleton working correctly.")
        else:
            print("✗ Database manager singleton not working.")
            return False
        
        # Test manager operations
        with manager1.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            count = cursor.fetchone()[0]
            print(f"✓ Database manager operations successful. Found {count} users.")
            return True
            
    except Exception as e:
        print(f"✗ Database manager test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Database Connection Test Suite")
    print("=" * 40)
    
    tests = [
        test_basic_connection,
        test_concurrent_reads,
        test_read_write_operations,
        test_database_lock_detection,
        test_database_manager,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            print()
    
    print("=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Database connections are working properly.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
