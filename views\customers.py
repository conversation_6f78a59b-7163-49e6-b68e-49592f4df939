import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import os
import re
from datetime import datetime
from resources import resource_path
from PIL import Image, ImageTk
import pandas as pd
import time
import logging
try:
    from login import current_user
except ImportError:
    current_user = None
from database import Database

class Tooltip:
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip = None
        self.widget.bind("<Enter>", self.enter)
        self.widget.bind("<Leave>", self.leave)
        self.widget.bind("<ButtonPress>", self.leave)  # Close on click

    def enter(self, event=None):
        self.tooltip = tk.Toplevel(self.widget)
        self.tooltip.wm_overrideredirect(True)
        self.tooltip.wm_attributes("-topmost", True)
        
        label = tk.Label(
            self.tooltip,
            text=self.text,
            background="#4A6FA5",
            relief="solid",
            borderwidth=1,
            font=("Helvetica", 10),
            fg="#FFFFFF"
        )
        label.pack()
        
        widget_x = self.widget.winfo_rootx()
        widget_y = self.widget.winfo_rooty()
        widget_width = self.widget.winfo_width()
        widget_height = self.widget.winfo_height()
        
        self.tooltip.update_idletasks()
        tooltip_width = self.tooltip.winfo_width()
        tooltip_height = self.tooltip.winfo_height()
        
        screen_width = self.widget.winfo_screenwidth()
        screen_height = self.widget.winfo_screenheight()
        
        x = widget_x + (widget_width - tooltip_width) // 2
        y = widget_y + widget_height + 5
        
        if x + tooltip_width > screen_width:
            x = screen_width - tooltip_width - 5
        elif x < 0:
            x = 5
            
        if y + tooltip_height > screen_height:
            y = widget_y - tooltip_height - 5
            if y < 0:
                y = 5
        
        self.tooltip.wm_geometry(f"+{int(x)}+{int(y)}")

    def leave(self, event=None):
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None

class CustomerManager(tk.Frame):
    COLORS = {
        'background': '#F8F9FA',
        'card_bg': '#FFFFFF',
        'primary_accent': '#4A6FA5',
        'secondary_accent': '#6C8FC7',
        'text_primary': '#2D3748',
        'text_secondary': '#718096',
        'button_start': '#4A6FA5',
        'button_end': '#3A5A8C',
        'transparent': 'transparent',
        'warning': '#E53E3E',
        'input_bg': '#EDF2F7',
        'border': '#E2E8F0',
        'suspended_row': '#FDF6BE'
    }

    def __init__(self, parent, nav_commands):
        super().__init__(parent)
        self.nav = nav_commands
        self.current_year = datetime.now().year
        self.current_month = datetime.now().month
        self.db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
        self._dialogs = []  # Track open dialogs

        self._update_customer_table_schema()

        self.config(bg=self.COLORS['background'])
        self.pack(fill="both", expand=True)

        # Header
        header = tk.Frame(self, bg=self.COLORS['primary_accent'], height=80)
        header.pack(fill="x", pady=(0, 20))

        # Dashboard Button with Icon
        self.icons = {}
        icon_paths = {
            'dashboard': 'assets/dashboard/customers/dashboard.png',
            'bill_details': 'assets/dashboard/customers/billdetails.png',
            'edit': 'assets/dashboard/customers/edituser.png',
            'add_customer': 'assets/dashboard/customers/adduser.png',
            'activate': 'assets/dashboard/customers/activateuser.png',
            'suspend': 'assets/dashboard/customers/suspenduser.png',
            'change_plan': 'assets/dashboard/customers/changepackageplan.png',
            'buy_product': 'assets/dashboard/products/buyproduct.png',
            'billing_nav': 'assets/dashboard/billing/left-arrow.png'
        }

        for icon_name, relative_path in icon_paths.items():
            try:
                full_path = resource_path(relative_path)
                if os.path.exists(full_path):
                    img = Image.open(full_path).convert("RGBA")
                    data = img.getdata()
                    new_data = []
                    for item in data:
                        if item[3] > 0:  # If pixel is not transparent
                            new_data.append((255, 255, 255, item[3]))  # Set to white
                        else:
                            new_data.append(item)
                    img.putdata(new_data)
                    img = img.resize((30, 30), Image.Resampling.LANCZOS)
                    self.icons[icon_name] = ImageTk.PhotoImage(img)
                else:
                    print(f"Icon not found: {full_path}")
                    self.icons[icon_name] = ImageTk.PhotoImage(Image.new('RGBA', (30, 30), (255, 255, 255, 0)))
            except Exception as e:
                print(f"Error loading icon {icon_name}: {str(e)}")
                self.icons[icon_name] = ImageTk.PhotoImage(Image.new('RGBA', (30, 30), (255, 255, 255, 0)))

        dashboard_btn = tk.Button(header, image=self.icons['dashboard'], command=self.nav['show_dashboard'],
                                 bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                 activebackground=self.COLORS['secondary_accent'])
        dashboard_btn.pack(side="left", padx=10)
        Tooltip(dashboard_btn, "Dashboard")

        tk.Label(header, text="Customer Management", font=("Helvetica", 24, "bold"),
                 fg="#FFFFFF", bg=self.COLORS['primary_accent']).place(relx=0.5, rely=0.5, anchor="center")

        # Action Buttons in Header (Right-aligned)
        btn_frame = tk.Frame(header, bg=self.COLORS['primary_accent'])
        btn_frame.pack(side="right", padx=10)

        billing_nav_btn = tk.Button(btn_frame, image=self.icons['billing_nav'], command=self.nav['show_billing'],
                                   bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                   activebackground=self.COLORS['secondary_accent'])
        billing_nav_btn.pack(side="left", padx=5)
        Tooltip(billing_nav_btn, "Billing")

        self.bill_btn = tk.Button(btn_frame, image=self.icons['bill_details'], command=self._show_billing,
                                 bg=self.COLORS['primary_accent'], relief=tk.FLAT, state="disabled",
                                 activebackground=self.COLORS['secondary_accent'])
        self.bill_btn.pack(side="left", padx=5)
        Tooltip(self.bill_btn, "Bill Details")

        self.edit_btn = tk.Button(btn_frame, image=self.icons['edit'], command=self._edit_customer,
                                 bg=self.COLORS['primary_accent'], relief=tk.FLAT, state="disabled",
                                 activebackground=self.COLORS['secondary_accent'])
        self.edit_btn.pack(side="left", padx=5)
        Tooltip(self.edit_btn, "Edit")

        add_customer_btn = tk.Button(btn_frame, image=self.icons['add_customer'], command=self._add_customer,
                                    bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                    activebackground=self.COLORS['secondary_accent'])
        add_customer_btn.pack(side="left", padx=5)
        Tooltip(add_customer_btn, "Add Customer")

        self.active_btn = tk.Button(btn_frame, image=self.icons['activate'], command=self._activate_customer,
                                   bg=self.COLORS['primary_accent'], relief=tk.FLAT, state="disabled",
                                   activebackground=self.COLORS['secondary_accent'])
        self.active_btn.pack(side="left", padx=5)
        Tooltip(self.active_btn, "Activate")

        self.suspend_btn = tk.Button(btn_frame, image=self.icons['suspend'], command=self._suspend_customer,
                                    bg=self.COLORS['primary_accent'], relief=tk.FLAT, state="disabled",
                                    activebackground=self.COLORS['secondary_accent'])
        self.suspend_btn.pack(side="left", padx=5)
        Tooltip(self.suspend_btn, "Suspend")

        self.change_plan_btn = tk.Button(btn_frame, image=self.icons['change_plan'], command=self._change_package_plan,
                                       bg=self.COLORS['primary_accent'], relief=tk.FLAT, state="disabled",
                                       activebackground=self.COLORS['secondary_accent'])
        self.change_plan_btn.pack(side="left", padx=5)
        Tooltip(self.change_plan_btn, "Change Package Plan")

        self.buy_product_btn = tk.Button(btn_frame, image=self.icons['buy_product'], command=self._buy_product,
                                        bg=self.COLORS['primary_accent'], relief=tk.FLAT, state="disabled",
                                        activebackground=self.COLORS['secondary_accent'])
        self.buy_product_btn.pack(side="left", padx=5)
        Tooltip(self.buy_product_btn, "Buy Product")

        # Main Content
        content = tk.Frame(self, bg=self.COLORS['card_bg'])
        content.pack(fill="both", expand=True, padx=5, pady=5)

        # Filter and Search Controls
        filter_frame = tk.Frame(content, bg=self.COLORS['card_bg'])
        filter_frame.pack(fill="x", pady=(0, 5))

        tk.Label(filter_frame, text="Filter by Region:", bg=self.COLORS['card_bg'],
                 fg=self.COLORS['text_primary'], font=("Helvetica", 10)).pack(side="left", padx=5)

        self.region_filter_var = tk.StringVar(value="All")
        self.region_filter_dropdown = ttk.Combobox(filter_frame, textvariable=self.region_filter_var,
                                                  state="readonly", font=("Helvetica", 12), width=20)
        self.region_filter_dropdown.pack(side="left", padx=5)
        self.region_filter_dropdown.bind("<<ComboboxSelected>>", lambda e: self._load_customers())

        tk.Label(filter_frame, text="Search Customer:", font=("Helvetica", 10),
                 bg=self.COLORS['card_bg'], fg=self.COLORS['text_primary']).pack(side="left", padx=(10, 5))

        self.customer_search_var = tk.StringVar()
        self.customer_search_var.trace('w', self._on_search_change)
        
        self.customer_combobox = ttk.Combobox(filter_frame, textvariable=self.customer_search_var,
                                             font=("Helvetica", 12), width=20)
        self.customer_combobox.pack(side="left", padx=5)
        self.customer_combobox.bind("<<ComboboxSelected>>", lambda e: self._on_customer_selected())

        # Import, Export, and Billing Buttons
        import_btn = tk.Button(filter_frame, text="Import", command=self._import_customers,
                              bg=self.COLORS['button_start'], fg="#FFFFFF",
                              font=("Helvetica", 10, "bold"), relief=tk.FLAT,
                              activebackground=self.COLORS['button_end'])
        import_btn.pack(side="left", padx=5)
        Tooltip(import_btn, "Import Customers from Excel")

        export_btn = tk.Button(filter_frame, text="Export All", command=self._export_customers,
                              bg=self.COLORS['button_start'], fg="#FFFFFF",
                              font=("Helvetica", 10, "bold"), relief=tk.FLAT,
                              activebackground=self.COLORS['button_end'])
        export_btn.pack(side="left", padx=5)
        Tooltip(export_btn, "Export All Customer Data with Balances")

        # Customer Table
        tree_frame = tk.Frame(content, bg=self.COLORS['card_bg'])
        tree_frame.pack(fill="both", expand=True, pady=(0, 5))

        # Configure Treeview style
        style = ttk.Style()
        style.configure("Treeview",
                        background=self.COLORS['card_bg'],
                        foreground=self.COLORS['text_secondary'],
                        fieldbackground=self.COLORS['card_bg'],
                        font=("Helvetica", 10),
                        rowheight=25,
                        borderwidth=1,
                        relief="solid",
                        bordercolor=self.COLORS['border'])

        style.configure("Treeview.Heading",
                        font=("Helvetica", 10, "bold"),
                        foreground=self.COLORS['text_secondary'],
                        background=self.COLORS['input_bg'],
                        relief="flat",
                        padding=5)

        style.configure("Suspended.Treeview",
                        background=self.COLORS['suspended_row'],
                        foreground=self.COLORS['text_secondary'])

        style.map("Treeview", 
                  background=[('selected', self.COLORS['primary_accent'])],
                  foreground=[('selected', '#FFFFFF')])

        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL)
        scrollbar.pack(side="right", fill="y")

        columns = ("id", "user_name", "name", "phone", "package", "region", "status", "outstanding", "credit")
        self.tree = ttk.Treeview(tree_frame, 
                                columns=columns,
                                selectmode="browse",
                                show="headings",
                                yscrollcommand=scrollbar.set,
                                style="Treeview")
        for col in columns:
            if col in ("user_name", "name"):
                self.tree.heading(col, text=col.replace("_", " ").title(), anchor="w")
            else:
                self.tree.heading(col, text=col.replace("_", " ").title(), anchor="center")

        self.tree.column("id", width=50, anchor="center")
        self.tree.column("user_name", width=120, anchor="w")
        self.tree.column("name", width=150, anchor="w")
        self.tree.column("phone", width=120, anchor="center")
        self.tree.column("package", width=80, anchor="center")
        self.tree.column("region", width=80, anchor="center")
        self.tree.column("status", width=80, anchor="center")
        self.tree.column("outstanding", width=100, anchor="center")
        self.tree.column("credit", width=100, anchor="center")

        scrollbar.config(command=self.tree.yview)
        self.tree.pack(fill="both", expand=True)

        self.tree.bind("<<TreeviewSelect>>", self._on_row_select)
        self._load_regions()
        self._load_customers()
        self._load_packages()
        
        # Check for month end carry forward
        # self._check_and_handle_month_end_carry_forward()  # <-- Remove or comment out this line

        # Bind minimize/restore events to main window
        self.winfo_toplevel().bind('<Unmap>', self._on_main_minimize)
        self.winfo_toplevel().bind('<Map>', self._on_main_restore)

    def _update_customer_table_schema(self):
        try:
            with sqlite3.connect(self.db_path, timeout=10) as conn:
                c = conn.cursor()
                c.execute("PRAGMA table_info(customers)")
                columns = {col[1]: col for col in c.fetchall()}

                if 'status' not in columns:
                    c.execute("ALTER TABLE customers ADD COLUMN status INTEGER DEFAULT 1")
                elif columns['status'][2] != 'INTEGER':
                    c.execute('''CREATE TABLE customers_new (
                                 id INTEGER PRIMARY KEY AUTOINCREMENT,
                                 user_name TEXT NOT NULL UNIQUE,
                                 name TEXT NOT NULL,
                                 phone TEXT,
                                 package_id INTEGER,
                                 create_date TEXT,
                                 status INTEGER DEFAULT 1,
                                 package_change_date TEXT,
                                 region TEXT,
                                 FOREIGN KEY (package_id) REFERENCES packages(id)
                                 )''')
                    c.execute('''INSERT INTO customers_new (id, user_name, name, phone, package_id, create_date, status, region)
                                 SELECT id, name, name, phone, package_id, create_date,
                                        CASE WHEN status = 'Active' THEN 1 ELSE 0 END, region
                                 FROM customers''')
                    c.execute("DROP TABLE customers")
                    c.execute("ALTER TABLE customers_new RENAME TO customers")

                if 'package_change_date' not in columns:
                    c.execute("ALTER TABLE customers ADD COLUMN package_change_date TEXT")

                if 'region' not in columns:
                    c.execute("ALTER TABLE customers ADD COLUMN region TEXT")

                if 'outstanding_amount' not in columns:
                    c.execute("ALTER TABLE customers ADD COLUMN outstanding_amount REAL DEFAULT 0.0")
                
                if 'credit_balance' not in columns:
                    c.execute("ALTER TABLE customers ADD COLUMN credit_balance REAL DEFAULT 0.0")

                if 'user_name' not in columns:
                    c.execute("ALTER TABLE customers ADD COLUMN user_name TEXT")
                    c.execute("UPDATE customers SET user_name = name WHERE user_name IS NULL")
                    c.execute("ALTER TABLE customers RENAME TO customers_old")
                    c.execute('''CREATE TABLE customers (
                                 id INTEGER PRIMARY KEY AUTOINCREMENT,
                                 user_name TEXT NOT NULL UNIQUE,
                                 name TEXT NOT NULL,
                                 phone TEXT,
                                 package_id INTEGER,
                                 create_date TEXT,
                                 status INTEGER DEFAULT 1,
                                 package_change_date TEXT,
                                 region TEXT,
                                 FOREIGN KEY (package_id) REFERENCES packages(id)
                                 )''')
                    c.execute('''INSERT INTO customers (id, user_name, name, phone, package_id, create_date, status, package_change_date, region)
                                 SELECT id, user_name, name, phone, package_id, create_date, status, package_change_date, region
                                 FROM customers_old''')
                    c.execute("DROP TABLE customers_old")

                conn.commit()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to update customer table schema: {str(e)}")

    def _validate_phone(self, phone):
        if not phone:
            return True
        return re.match(r'^03\d{9}$', phone) is not None

    def _validate_user_name(self, user_name):
        if not user_name:
            raise ValueError("User name is required")
        if len(user_name) > 20:
            raise ValueError("User name must not exceed 20 characters")
        if not re.match(r'^[a-zA-Z0-9.]+$', user_name):
            raise ValueError("User name must contain only letters, numbers, and dots")
        return user_name

    def _on_row_select(self, event):
        selected = self.tree.selection()
        if selected:
            self.bill_btn.config(state="normal")
            self.edit_btn.config(state="normal")
            self.change_plan_btn.config(state="normal")
            self.buy_product_btn.config(state="normal")
            customer_status = self.tree.item(selected[0])['values'][6]
            if customer_status == 'Active':
                self.active_btn.config(state="disabled")
                self.suspend_btn.config(state="normal")
            else:
                self.active_btn.config(state="normal")
                self.suspend_btn.config(state="disabled")
        else:
            self.bill_btn.config(state="disabled")
            self.edit_btn.config(state="disabled")
            self.active_btn.config(state="disabled")
            self.suspend_btn.config(state="disabled")
            self.change_plan_btn.config(state="disabled")
            self.buy_product_btn.config(state="disabled")

    def _load_regions(self):
        try:
            with sqlite3.connect(self.db_path, timeout=10) as conn:
                c = conn.cursor()
                c.execute("SELECT name FROM regions ORDER BY name")
                self.regions = [row[0] for row in c.fetchall()]
                self.region_filter_dropdown['values'] = ['All'] + self.regions
                if not self.region_filter_var.get():
                    self.region_filter_var.set('All')
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load regions: {str(e)}")
            self.regions = []

    def _load_customers(self):
        """Load customers from database with modified logic to preserve imported values"""
        try:
            with sqlite3.connect(self.db_path, timeout=10) as conn:
                c = conn.cursor()
                
                # Get all customers with their package details
                query = '''
                    SELECT c.id, c.user_name, c.name, c.phone, p.name as package_name, 
                           c.region, c.status, c.outstanding_amount, c.credit_balance,
                           (SELECT COUNT(*) FROM payment_history 
                            WHERE customer_id = c.id AND paid_by = 'System Import' 
                            AND payment_date >= date('now', '-1 day')) as recently_imported
                    FROM customers c
                    LEFT JOIN packages p ON c.package_id = p.id
                    ORDER BY c.user_name
                '''
                
                c.execute(query)
                customers = c.fetchall()
                
                # Clear existing data
                for item in self.tree.get_children():
                    self.tree.delete(item)
                
                # Populate treeview with customer data
                for customer in customers:
                    customer_id = customer[0]
                    recently_imported = customer[9] > 0 if len(customer) > 9 else False
                    
                    # Only recalculate financial data if not recently imported
                    if not recently_imported:
                        # Get outstanding amount from unpaid bills
                        c.execute('''
                            SELECT SUM(amount - COALESCE(paid_amount, 0)) 
                            FROM billing 
                            WHERE customer_id = ? AND status = 'Unpaid'
                        ''', (customer_id,))
                        calculated_outstanding = c.fetchone()[0] or 0.0
                        
                        # Get credit from last paid bill
                        c.execute('''
                            SELECT credit_amount FROM billing
                            WHERE customer_id = ? AND status = 'Paid'
                            ORDER BY year DESC, month DESC, id DESC
                            LIMIT 1
                        ''', (customer_id,))
                        bill_credit = c.fetchone()
                        bill_credit_amount = bill_credit[0] if bill_credit and bill_credit[0] is not None else 0.0
                        
                        # Use the stored values from customer table (imported or previously calculated)
                        stored_outstanding = customer[7] if customer[7] is not None else 0.0
                        stored_credit = customer[8] if customer[8] is not None else 0.0
                        
                        # Use the maximum of stored credit and bill credit to ensure consistency
                        final_credit = max(stored_credit, bill_credit_amount)
                        
                        # Use calculated outstanding if it's greater than stored (for accuracy)
                        final_outstanding = max(stored_outstanding, calculated_outstanding)
                        
                        # Update customer record if values differ
                        if abs(final_outstanding - stored_outstanding) > 0.01 or abs(final_credit - stored_credit) > 0.01:
                            c.execute('''
                                UPDATE customers 
                                SET outstanding_amount = ?, credit_balance = ? 
                                WHERE id = ?
                            ''', (final_outstanding, final_credit, customer_id))
                    else:
                        # For recently imported customers, use the imported values as-is
                        final_outstanding = customer[7] if customer[7] is not None else 0.0
                        final_credit = customer[8] if customer[8] is not None else 0.0
                    
                    # Format values for display
                    status_text = "Active" if customer[6] == 1 else "Inactive"
                    package_name = customer[4] or ""
                    region = customer[5] or ""
                    
                    self.tree.insert('', 'end', values=(
                        customer[0],  # ID
                        customer[1],  # User Name
                        customer[2],  # Name
                        customer[3] or "",  # Phone
                        package_name,  # Package
                        region,  # Region
                        status_text,  # Status
                        f"{final_outstanding:.2f}",  # Outstanding
                        f"{final_credit:.2f}"  # Credit
                    ))
                
                conn.commit()
                
        except Exception as e:
            logging.error(f"Error loading customers: {str(e)}")
            messagebox.showerror("Error", f"Failed to load customers: {str(e)}")

    def _load_packages(self):
        try:
            with sqlite3.connect(self.db_path, timeout=10) as conn:
                c = conn.cursor()

                c.execute("SELECT id, name, price FROM packages")
                self.packages = {name: (id, price) for id, name, price in c.fetchall()}
                
                c.execute("SELECT user_name FROM customers")
                self.customer_names = sorted([row[0] for row in c.fetchall()])
                self.customer_combobox['values'] = self.customer_names
                self.customers = {name: name for name in self.customer_names}
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load packages: {str(e)}")
            self.packages = {}
            self.customer_names = []
            self.customers = {}

    def _on_search_change(self, *args):
        search_term = self.customer_search_var.get().lower()
        if not hasattr(self, 'customer_names'):
            return

        if not search_term:
            self.customer_combobox['values'] = self.customer_names
            self._load_customers()
        else:
            filtered = [name for name in self.customer_names if name.lower().startswith(search_term)]
            self.customer_combobox['values'] = filtered

    def _on_customer_selected(self):
        self._load_customers()

    def _limit_phone_input(self, new_value):
        return True  # No restrictions on phone input length

    def _format_user_name(self, user_name_var, entry_widget):
        current_text = user_name_var.get()
        cursor_pos = entry_widget.index(tk.INSERT)

        cleaned_text = re.sub(r'[^a-zA-Z0-9.]', '', current_text)

        if len(cleaned_text) > 20:
            cleaned_text = cleaned_text[:20]
            messagebox.showwarning("Input Limit", "User name cannot exceed 20 characters.")

        user_name_var.set(cleaned_text)
        new_cursor_pos = min(cursor_pos, len(cleaned_text))
        entry_widget.icursor(new_cursor_pos)

    def _format_name(self, name_var, entry_widget):
        current_text = name_var.get()
        cursor_pos = entry_widget.index(tk.INSERT)

        cleaned_text = re.sub(r'[^a-zA-Z\s]', '', current_text)
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text)

        parts = cleaned_text.split()
        if len(parts) > 3:
            cleaned_text = ' '.join(parts[:3])

        if cleaned_text:
            words = cleaned_text.split()
            formatted = ' '.join(word.capitalize() for word in words)
        else:
            formatted = ''

        if len(formatted) > 20:
            formatted = formatted[:20]
            if ' ' in formatted:
                formatted = formatted.rsplit(' ', 1)[0]
            messagebox.showwarning("Input Limit", "Name cannot exceed 20 characters.")

        name_var.set(formatted)
        new_cursor_pos = min(cursor_pos, len(formatted))
        entry_widget.icursor(new_cursor_pos)

    def _validate_full_name(self, name):
        if not name:
            raise ValueError("Customer name is required")
        if len(name) > 20:
            raise ValueError("Customer name must not exceed 20 characters")
        if not re.match(r'^[a-zA-Z\s]+$', name):
            raise ValueError("Name must contain only letters and spaces")
        if " " not in name.strip():
            raise ValueError("Please enter a full name with a space, e.g., 'Imran Ameen'")
        parts = name.split()
        if len(parts) < 2 or not all(parts):
            raise ValueError("Both first name and last name are required, e.g., 'Imran Ameen'")
        return name

    def sync_customer_billing_data(self, customer_id):
        """Sync billing data with customer records - modified to preserve imported values"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                c = conn.cursor()
                # ... business logic ...
                # (move all code from original function here, replacing conn/c usage as needed)
                # ... existing code ...
        except Exception as e:
            logging.error(f"Error syncing billing data for customer {customer_id}: {str(e)}")

    def _export_customers(self):
        try:
            export_path = filedialog.asksaveasfilename(
                initialfile="customers_export.xlsx",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")],
                title="Select Export Destination"
            )
            if not export_path:
                return

            with sqlite3.connect(self.db_path, timeout=10) as conn:
                query = '''
                    SELECT 
                        c.id,
                        c.user_name,
                        c.name,
                        c.phone,
                        p.name as package,
                        c.region,
                        CASE WHEN c.status = 1 THEN 'Active' ELSE 'Inactive' END as status,
                        COALESCE(c.outstanding_amount, 0) as outstanding,
                        COALESCE(c.credit_balance, 0) as credit
                    FROM customers c
                    LEFT JOIN packages p ON c.package_id = p.id
                    ORDER BY c.user_name'''
                df = pd.read_sql_query(query, conn)

            # Format numeric columns
            df['outstanding'] = df['outstanding'].apply(lambda x: f"{float(x):.2f}")
            df['credit'] = df['credit'].apply(lambda x: f"{float(x):.2f}")

            df.to_excel(export_path, index=False)
            messagebox.showinfo("Success", "Customer data exported successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export customers: {str(e)}")

    
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export balances: {str(e)}")

    

    def _check_and_handle_month_end_carry_forward(self):
        """Check if it's the last day of the month and handle carry forward of unpaid bills"""
        try:
            # Get the file path for import
            file_path = filedialog.askopenfilename(
                title="Select Excel File to Import",
                filetypes=[("Excel files", "*.xlsx;*.xls")]
            )
            if not file_path:
                return
            
            # Read the Excel file
            df = pd.read_excel(file_path)
            
            # Set current_username for tracking who performed the import
            current_username = "System Import"
            
            # Confirm data wipe
            if not messagebox.askyesno("Warning", 
                                      "This will clear ALL existing customer and billing data and replace it with the imported data.\n\n"
                                      "Are you sure you want to continue?"):
                return
            
            with sqlite3.connect(self.db_path, timeout=10) as conn:
                c = conn.cursor()
                conn.execute("BEGIN TRANSACTION")

                # Clear existing data
                c.execute("DELETE FROM billing")
                c.execute("DELETE FROM customers")
                c.execute("DELETE FROM payment_history")
                c.execute("DELETE FROM customer_purchases")

                # Get package and region data
                c.execute("SELECT id, name, price FROM packages")
                package_data = c.fetchall()
                package_map = {name.lower(): (id, price) for id, name, price in package_data}

                c.execute("SELECT name FROM regions")
                valid_regions = {row[0].lower() for row in c.fetchall()}

                import_date = datetime.now().strftime('%Y-%m-%d')
                imported_ids = []
                # Get current max invoice number
                c.execute("SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER)) FROM billing WHERE invoice_number IS NOT NULL")
                max_inv_num = c.fetchone()[0] or 0
                next_inv_num = max_inv_num + 1

                for _, row in df.iterrows():
                    try:
                        user_name = str(row['user_name']).strip()
                        name = str(row['name']).strip()
                        phone = str(row['phone']).strip() if pd.notna(row['phone']) else None
                        package_name = str(row['package']).strip() if pd.notna(row['package']) else None
                        region = str(row['region']).strip() if pd.notna(row['region']) else None
                        status = 1 if str(row['status']).strip().lower() == 'active' else 0
                        outstanding = float(row['outstanding']) if pd.notna(row['outstanding']) else 0.0
                        credit = float(row['credit']) if pd.notna(row['credit']) else 0.0

                        if not user_name or not name or (package_name and package_name.lower() not in package_map):
                            continue  # skip invalid row

                        package_id, package_price = package_map[package_name.lower()] if package_name else (None, 0.0)
                        # Insert customer
                        c.execute('''INSERT INTO customers 
                            (id, user_name, name, phone, package_id, create_date, 
                              status, region, outstanding_amount, credit_balance)
                             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                  (row['id'], user_name, name, phone, package_id, import_date,
                                   status, region, outstanding, credit))
                        customer_id = row['id'] if 'id' in row and pd.notna(row['id']) else c.lastrowid
                        imported_ids.append(customer_id)

                        # ---- Handle billing import logic
                        if credit > 0 and outstanding == 0:
                            if credit < package_price:
                                try:
                                    # Generate invoice number
                                    invoice_number = f"INV-{str(next_inv_num).zfill(4)}"
                                    next_inv_num += 1
                                    payment_date = datetime.now().strftime('%Y-%m-%d')  # Ensure payment_date is defined
                                    # Create billing record showing partial payment
                                    c.execute('''INSERT INTO billing
                                        (customer_id, month, year, amount, paid_amount, status,
                                         paid_date, invoice_number, paid_by, outstanding_amount, credit_amount)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                        (customer_id, self.current_month, self.current_year,
                                         package_price,          # Full amount
                                         credit,                # Amount paid from credit
                                         'Partially Paid',      # Status
                                         payment_date,          # Now properly defined
                                         invoice_number,
                                         "Excel Import",
                                         package_price - credit, # Remaining outstanding
                                         0                      # Credit fully used
                                        ))
                                    print(f"[DEBUG] Inserted billing: customer_id={customer_id}, amount={package_price}, paid_amount={credit}, status=Partially Paid, invoice={invoice_number}")
                                    # Update customer's credit balance to 0
                                    c.execute('''UPDATE customers SET credit_balance = 0 WHERE id = ?''', 
                                             (customer_id,))
                                    # Create payment history record
                                    c.execute('''INSERT INTO payment_history
                                        (invoice_number, customer_id, payment_date, amount_paid,
                                         credit_amount, outstanding_amount, paid_by)
                                        VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                        (invoice_number, customer_id, payment_date,
                                         credit,         # Amount paid
                                         credit,         # Credit used
                                         package_price - credit,  # Remaining outstanding
                                         "Excel Import"
                                        ))
                                    print(f"[DEBUG] Inserted payment_history: invoice={invoice_number}, amount_paid={credit}, outstanding={package_price - credit}")
                                    self._update_progress(progress_window, idx + 1, f"Imported row {idx+2} (credit applied as partial payment, bill and history created)")
                                except Exception as e:
                                    logging.error(f"Error inserting partial credit billing/payment for row {idx+2}: {e}")
                                    import_result['failed_imports'] += 1
                                    import_result['failed_rows'].append({
                                        'row': idx + 2,
                                        'user_name': str(row.get('user_name', '')),
                                        'errors': [str(e)]
                                    })
                                    continue
                            else:
                                remaining_credit = credit
                                future_month = self.current_month
                                future_year = self.current_year
                                last_bill_id = None
                                months_paid = 0
                                while remaining_credit >= package_price:
                                    invoice_number = f"INV-{str(next_inv_num).zfill(4)}"
                                    c.execute('''INSERT INTO billing
                                        (customer_id, month, year, amount, paid_amount, status, paid_date, invoice_number, paid_by, outstanding_amount, credit_amount)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                        (customer_id, future_month, future_year, package_price, package_price, 'Paid', import_date, invoice_number, "Excel Import", 0, 0))
                                    last_bill_id = c.lastrowid
                                    remaining_credit -= package_price
                                    months_paid += 1
                                    next_inv_num += 1
                                    future_month += 1
                                    if future_month > 12:
                                        future_month = 1
                                        future_year += 1
                                if remaining_credit > 0 and last_bill_id is not None:
                                    c.execute('''UPDATE billing SET credit_amount = ? WHERE id = ?''', (remaining_credit, last_bill_id))
                                c.execute('''UPDATE customers SET credit_balance = 0, outstanding_amount = 0 WHERE id = ?''', (customer_id,))
                                total_paid = months_paid * package_price
                                c.execute('''INSERT INTO payment_history (invoice_number, customer_id, payment_date, amount_paid, credit_amount, outstanding_amount, paid_by)
                                              VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                            (f"INV-{str(next_inv_num-1).zfill(4)}", customer_id, import_date, total_paid, remaining_credit, 0, "Excel Import"))
                                self._update_progress(progress_window, idx + 1, f"Imported row {idx+2}")
                        elif outstanding > 0 and credit == 0:
                            invoice_number = f"INV-{str(next_inv_num).zfill(4)}"
                            bill_amount = outstanding + (package_price if status == 1 and package_id else 0)
                            c.execute('''INSERT INTO billing
                                (customer_id, month, year, amount, paid_amount, status, paid_date, invoice_number, paid_by, outstanding_amount, credit_amount)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                (customer_id, self.current_month, self.current_year, bill_amount, 0, 'Unpaid', import_date, invoice_number, "Excel Import", outstanding, 0))
                            c.execute('''UPDATE customers SET credit_balance = 0, outstanding_amount = ? WHERE id = ?''', (outstanding, customer_id))
                            c.execute('''INSERT INTO payment_history (invoice_number, customer_id, payment_date, amount_paid, credit_amount, outstanding_amount, paid_by)
                                          VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                       (invoice_number, customer_id, import_date, 0, 0, outstanding, "Excel Import"))
                            next_inv_num += 1
                        elif credit == 0 and outstanding == 0:
                            invoice_number = f"INV-{str(next_inv_num).zfill(4)}"
                            c.execute('''INSERT INTO billing
                                (customer_id, month, year, amount, paid_amount, status, paid_date, invoice_number, paid_by, outstanding_amount, credit_amount)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                (customer_id, self.current_month, self.current_year, package_price, 0, 'Unpaid', import_date, invoice_number, "Excel Import", 0, 0))
                            c.execute('''UPDATE customers SET credit_balance = 0, outstanding_amount = 0 WHERE id = ?''', (customer_id,))
                            c.execute('''INSERT INTO payment_history (invoice_number, customer_id, payment_date, amount_paid, credit_amount, outstanding_amount, paid_by)
                                          VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                       (invoice_number, customer_id, import_date, 0, 0, 0, "Excel Import"))
                            next_inv_num += 1
                    except Exception as e:
                        logging.error(f"Error processing row {_}: {str(e)}")
                        continue

                conn.commit()
                messagebox.showinfo("Import Complete", f"Successfully imported {len(imported_ids)} customers.")
                self._load_customers()
                return imported_ids

        except Exception as e:
            if 'conn' in locals():
                conn.rollback()
            messagebox.showerror("Import Error", f"Failed to import customers: {str(e)}")
            logging.error(f"Import error: {str(e)}")
            return []

    def _buy_product(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a customer to buy products")
            return

        customer_id = self.tree.item(selected[0])['values'][0]
        customer_name = self.tree.item(selected[0])['values'][2]

        try:
            with sqlite3.connect(self.db_path, timeout=10) as conn:
                c = conn.cursor()
                c.execute("SELECT id, name, price FROM products")
                products = c.fetchall()
                c.execute("SELECT product_id, quantity, sold FROM stock")
                stock_data = {row[0]: {'quantity': row[1], 'sold': row[2]} for row in c.fetchall()}
                if not products:
                    messagebox.showerror("Error", "No products available to buy.")
                    return
                
                # Get customer's credit balance (both imported and from bills)
                c.execute("SELECT credit_balance FROM customers WHERE id = ?", (customer_id,))
                credit_balance = c.fetchone()[0] or 0.0
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load products: {str(e)}")
            return

        dialog = self._create_dialog()
        dialog.title(f"Buy Product for {customer_name}")
        dialog.configure(bg=self.COLORS['background'])
        dialog.geometry("560x600")

        main_frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], highlightbackground=self.COLORS['border'],
                             highlightthickness=1, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        tk.Label(main_frame, text=f"Customer: {customer_name}", **label_style).pack(anchor="w", pady=(0, 20))
        
        # Display available credit
        tk.Label(main_frame, text=f"Available Credit: {credit_balance:.2f} PKR", **label_style).pack(anchor="w", pady=(0, 10))
        
        tk.Label(main_frame, text="Select Products:", **label_style).pack(anchor="w", pady=(0, 10))
        product_vars = {}
        for product_id, name, price in products:
            var = tk.IntVar()
            product_vars[(product_id, name, price)] = var
            
            remaining = 0
            if product_id in stock_data:
                remaining = stock_data[product_id]['quantity'] - stock_data[product_id]['sold']
            
            frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
            frame.pack(anchor="w", pady=5)
            
            tk.Checkbutton(frame, text=f"{name} ({price:.2f} PKR)", variable=var,
                          bg=self.COLORS['card_bg'], fg=self.COLORS['text_primary'], selectcolor=self.COLORS['input_bg']).pack(side="left")
            tk.Label(frame, text=f"Available: {remaining}", **label_style).pack(side="left", padx=10)

        total_label = tk.Label(main_frame, text="Total: 0.00 PKR", **label_style)
        total_label.pack(pady=(20, 10))
        
        # Add label to show amount after credit
        after_credit_label = tk.Label(main_frame, text="Amount After Credit: 0.00 PKR", **label_style)
        after_credit_label.pack(pady=(0, 10))

        def update_total():
            total = sum(price for (pid, name, price), var in product_vars.items() if var.get() == 1)
            total_label.config(text=f"Total: {total:.2f} PKR")
            
            # Calculate amount after applying credit
            amount_after_credit = max(0, total - credit_balance)
            after_credit_label.config(text=f"Amount After Credit: {amount_after_credit:.2f} PKR")

        for (pid, name, price), var in product_vars.items():
            var.trace_add("write", lambda *args: update_total())

        def submit():
            selected_products = [(pid, name, price) for (pid, name, price), var in product_vars.items()
                               if var.get() == 1]
            if not selected_products:
                messagebox.showerror("Error", "Please select at least one product to buy")
                return

            try:
                with sqlite3.connect(self.db_path, timeout=10) as conn:
                    c = conn.cursor()
                    conn.execute("BEGIN TRANSACTION")
                    db = Database()
                    total_product_amount = sum(price for pid, name, price in selected_products)

                    # Use imported outstanding first, then imported credit, then bill credit
                    used_outstanding, remaining = db.use_imported_outstanding(customer_id, total_product_amount)
                    used_credit, remaining = db.use_imported_credit(customer_id, remaining)
                    # Now get remaining credit from last paid bill (credit_amount)
                    c.execute('''SELECT id, credit_amount FROM billing
                        WHERE customer_id = ? AND status = 'Paid'
                        ORDER BY year DESC, month DESC, id DESC
                        LIMIT 1''', (customer_id,))
                    last_bill_credit = c.fetchone()
                    last_paid_bill_id = last_bill_credit[0] if last_bill_credit else None
                    last_bill_credit_amount = last_bill_credit[1] if last_bill_credit and last_bill_credit[1] is not None else 0.0
                    credit_used_from_bill = 0.0
                    if remaining > 0 and last_bill_credit_amount > 0:
                        credit_used_from_bill = min(last_bill_credit_amount, remaining)
                        c.execute('''UPDATE billing SET credit_amount = credit_amount - ? WHERE id = ?''', (credit_used_from_bill, last_paid_bill_id))
                        remaining -= credit_used_from_bill
                    amount_due = max(0, remaining)

                    # After transaction, only one of credit or outstanding can remain
                    new_credit = 0.0
                    new_outstanding = 0.0
                    if amount_due > 0:
                        new_outstanding = amount_due
                    # Generate unique invoice number as in billing page
                    c.execute("SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER)) FROM billing WHERE invoice_number IS NOT NULL")
                    max_inv_num = c.fetchone()[0] or 0
                    new_inv_num = max_inv_num + 1
                    invoice_number = f"INV-{str(new_inv_num).zfill(4)}"

                    # Insert billing record
                    c.execute('''INSERT INTO billing 
                                 (customer_id, month, year, amount, is_paid, is_manual, outstanding_amount, credit_amount, invoice_number)
                                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                              (customer_id, self.current_month, self.current_year, total_product_amount, 1 if amount_due == 0 else 0, 0, new_outstanding, used_credit + credit_used_from_bill, invoice_number))
                    bill_id = c.lastrowid
                    purchase_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                    for product_id, name, price in selected_products:
                        c.execute('''INSERT INTO customer_purchases (customer_id, product_id, billing_id, purchase_date)
                                     VALUES (?, ?, ?, ?)''',
                                  (customer_id, product_id, bill_id, purchase_date))
                        c.execute("UPDATE stock SET sold = sold + 1 WHERE product_id = ?", (product_id,))

                    # Update customers table: only one of credit or outstanding can remain
                    c.execute("UPDATE customers SET credit_balance = ?, outstanding_amount = ? WHERE id = ?", (new_credit, new_outstanding, customer_id))
                    # Sync customer financial data (preserve imported values)
                    db.sync_customer_financial_data(customer_id, preserve_imported_values=True)
                    conn.commit()
                messagebox.showinfo("Success", f"Products purchased successfully for {customer_name}.")
                dialog.destroy()
                self._load_customers()
                self.nav['refresh_billing']()
                if 'refresh_stock' in self.nav:
                    self.nav['refresh_stock']()
            except Exception as e:
                conn.rollback()
                messagebox.showerror("Error", f"Failed to process purchase: {str(e)}")

        button_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        button_frame.pack(pady=20)
        tk.Button(button_frame, text="Buy", command=submit, bg=self.COLORS['button_start'],
                  fg="#FFFFFF", font=("Helvetica", 10, "bold"), relief=tk.FLAT, padx=20, pady=10,
                  activebackground=self.COLORS['button_end']).pack()

    def _suspend_customer(self):
        selected = self.tree.selection()
        if not selected:
            return

        customer_id = self.tree.item(selected[0])['values'][0]
        customer_name = self.tree.item(selected[0])['values'][2]

        if messagebox.askyesno("Confirm", f"Suspend account for {customer_name}?"):
            try:
                with sqlite3.connect(self.db_path, timeout=10) as conn:
                    c = conn.cursor()
                    c.execute("UPDATE customers SET status = 0 WHERE id = ?", (customer_id,))
                    conn.commit()
                    messagebox.showinfo("Success", f"{customer_name} has been suspended.")
                    self.tree.item(selected[0], tags=('suspended',))
                    self._load_customers()
                    self._on_row_select(None)
            except Exception as e:
                messagebox.showerror("Error", f"Failed to suspend customer: {str(e)}")

    def _activate_customer(self):
        selected = self.tree.selection()
        if not selected:
            return

        customer_id = self.tree.item(selected[0])['values'][0]
        customer_name = self.tree.item(selected[0])['values'][2]
        current_package = self.tree.item(selected[0])['values'][4]

        dialog = self._create_dialog()
        dialog.title("Activate Customer")
        dialog.configure(bg=self.COLORS['background'])
        dialog.geometry("550x350")

        main_frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], 
                             highlightbackground=self.COLORS['border'], 
                             highlightthickness=2, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        tk.Label(main_frame, text="Activate Customer", bg=self.COLORS['card_bg'], 
                 font=("Helvetica", 16, "bold"), fg=self.COLORS['primary_accent']).pack(anchor="center", pady=(0, 20))

        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        dropdown_style = {"font": ("Helvetica", 12), "width": 30}

        try:
            with sqlite3.connect(self.db_path, timeout=10) as conn:
                c = conn.cursor()
                c.execute('''SELECT month, year, amount, is_paid
                             FROM billing 
                             WHERE customer_id = ? AND is_paid = 0''', (customer_id,))
                outstanding_bills = c.fetchall()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to fetch outstanding bills: {str(e)}")
            return

        total_outstanding = sum(bill[2] for bill in outstanding_bills)
        outstanding_text = "\n".join([f"Month {bill[0]:02d}/{bill[1]}: {bill[2]:.2f} PKR" for bill in outstanding_bills]) if outstanding_bills else "No outstanding bills."

        outstanding_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        outstanding_frame.pack(fill="x", pady=(0, 15))
        tk.Label(outstanding_frame, text="Outstanding Bills:", **label_style, width=15, anchor="w").pack(side="left")
        tk.Label(outstanding_frame, text=outstanding_text, **label_style, width=30, anchor="w", justify="left").pack(side="left")
        
        total_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        total_frame.pack(fill="x", pady=(0, 15))
        tk.Label(total_frame, text="Total Outstanding:", **label_style, width=15, anchor="w").pack(side="left")
        tk.Label(total_frame, text=f"{total_outstanding:.2f} PKR", **label_style, width=30, anchor="w").pack(side="left")

        package_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        package_frame.pack(fill="x", pady=(0, 15))
        tk.Label(package_frame, text="Select Package:", **label_style, width=15, anchor="w").pack(side="left")
        package_var = tk.StringVar(value=current_package)
        package_dropdown = ttk.Combobox(package_frame, textvariable=package_var, values=list(self.packages.keys()),
                                       state="readonly", **dropdown_style)
        package_dropdown.pack(side="left")

        total_amount_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        total_amount_frame.pack(fill="x", pady=(0, 30))
        total_amount_label = tk.Label(total_amount_frame, text="", **label_style, width=45, anchor="w")
        total_amount_label.pack(side="left")

        def update_total():
            try:
                package_name = package_var.get()
                if package_name:
                    package_price = self.packages[package_name][1]
                    current_bill_paid = any(bill[0] == self.current_month and bill[1] == self.current_year and bill[3] == 1 for bill in outstanding_bills)
                    if current_bill_paid and package_name == current_package:
                        total = total_outstanding
                    else:
                        total = total_outstanding + (0 if package_name == current_package and any(bill[0] == self.current_month and bill[1] == self.current_year for bill in outstanding_bills) else package_price)
                    total_amount_label.config(text=f"Total Amount: {total:.2f} PKR")
            except KeyError:
                total_amount_label.config(text="Please select a package")

        package_var.trace_add("write", lambda *args: update_total())
        update_total()

        def submit():
            try:
                package_name = package_var.get()
                if not package_name:
                    raise ValueError("Please select a package")

                with sqlite3.connect(self.db_path, timeout=10) as conn:
                    c = conn.cursor()

                    package_id, package_price = self.packages[package_name]
                    current_bill_paid = any(bill[0] == self.current_month and bill[1] == self.current_year and bill[3] == 1 for bill in outstanding_bills)
                    current_bill_exists = any(bill[0] == self.current_month and bill[1] == self.current_year for bill in outstanding_bills)

                    c.execute("UPDATE customers SET status = 1, package_id = ? WHERE id = ?", (package_id, customer_id))

                    if not current_bill_paid and not (package_name == current_package and current_bill_exists):
                        c.execute('''INSERT OR IGNORE INTO billing (customer_id, month, year, amount, is_paid, is_manual)
                                     VALUES (?, ?, ?, ?, ?, ?)''',
                                  (customer_id, self.current_month, self.current_year, package_price, 0, 0))

                    c.execute("UPDATE customers SET package_change_date = ? WHERE id = ?",
                              (datetime.now().strftime('%Y-%m-%d'), customer_id))

                    conn.commit()

                messagebox.showinfo("Success", f"{customer_name} has been activated. {'No new bill added as current month is paid.' if current_bill_paid else f'New bill of {package_price:.2f} PKR added for {self.current_month:02d}/{self.current_year}.' if not (package_name == current_package and current_bill_exists) else 'Outstanding bills retained.'}")
                dialog.destroy()
                self._load_customers()
                self.nav['refresh_billing']()
            except ValueError as ve:
                messagebox.showerror("Error", str(ve))
            except Exception as e:
                messagebox.showerror("Error", f"Failed to activate customer: {str(e)}")

        tk.Button(main_frame, text="Activate", command=submit, bg=self.COLORS['primary_accent'],
                  fg="#FFFFFF", font=("Helvetica", 12, "bold"), relief=tk.FLAT, padx=20, pady=10,
                  activebackground=self.COLORS['secondary_accent'], activeforeground="#FFFFFF").pack(anchor="center")

    def _change_package_plan(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a customer to change package plan")
            return

        customer_id = self.tree.item(selected[0])['values'][0]
        customer_name = self.tree.item(selected[0])['values'][2]
        current_package = self.tree.item(selected[0])['values'][4]

        try:
            with sqlite3.connect(self.db_path, timeout=10) as conn:
                c = conn.cursor()
                c.execute("SELECT package_change_date FROM customers WHERE id = ?", (customer_id,))
                change_date = c.fetchone()[0]
                if change_date:
                    change_dt = datetime.strptime(change_date, '%Y-%m-%d')
                    if change_dt.month == self.current_month and change_dt.year == self.current_year:
                        messagebox.showerror("Error", "Package can only be changed once per month. Wait until next month.")
                        return
        except Exception as e:
            messagebox.showerror("Error", f"Failed to check package change date: {str(e)}")
            return

        dialog = self._create_dialog()
        dialog.title("Change Package Plan")
        dialog.configure(bg=self.COLORS['background'])
        dialog.geometry("550x350")

        main_frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], 
                             highlightbackground=self.COLORS['border'], 
                             highlightthickness=2, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        tk.Label(main_frame, text="Change Package Plan", bg=self.COLORS['card_bg'], 
                 font=("Helvetica", 16, "bold"), fg=self.COLORS['primary_accent']).pack(anchor="center", pady=(0, 20))

        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        dropdown_style = {"font": ("Helvetica", 12), "width": 30}

        package_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        package_frame.pack(fill="x", pady=(0, 15))
        tk.Label(package_frame, text="Select New Package:", **label_style, width=15, anchor="w").pack(side="left")
        package_var = tk.StringVar()
        package_dropdown = ttk.Combobox(package_frame, textvariable=package_var, values=list(self.packages.keys()),
                                       state="readonly", **dropdown_style)
        package_dropdown.pack(side="left")

        spacer_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'], height=30)
        spacer_frame.pack(fill="x", pady=(0, 30))

        def submit():
            try:
                package_name = package_var.get()
                if not package_name:
                    raise ValueError("Please select a package")
                if package_name == current_package:
                    raise ValueError("Please select a different package")

                with sqlite3.connect(self.db_path, timeout=10) as conn:
                    c = conn.cursor()

                    package_id, package_price = self.packages[package_name]
                    c.execute("UPDATE customers SET package_id = ?, package_change_date = ? WHERE id = ?",
                              (package_id, datetime.now().strftime('%Y-%m-%d'), customer_id))

                    c.execute('''INSERT OR IGNORE INTO billing (customer_id, month, year, amount, is_paid)
                                 VALUES (?, ?, ?, ?, ?)''',
                              (customer_id, self.current_month, self.current_year, package_price, 0))

                    conn.commit()

                messagebox.showinfo("Success", f"Package plan for {customer_name} changed to {package_name}. New bill of {package_price:.2f} PKR added for {self.current_month:02d}/{self.current_year}.")
                dialog.destroy()
                self._load_customers()
                self.nav['refresh_billing']()
            except ValueError as ve:
                messagebox.showerror("Error", str(ve))
            except Exception as e:
                messagebox.showerror("Error", f"Failed to change package plan: {str(e)}")

        tk.Button(main_frame, text="Change Plan", command=submit, bg=self.COLORS['primary_accent'],
                  fg="#FFFFFF", font=("Helvetica", 12, "bold"), relief=tk.FLAT, padx=20, pady=10,
                  activebackground=self.COLORS['secondary_accent'], activeforeground="#FFFFFF").pack(anchor="center")

    def _add_customer(self):
        dialog = self._create_dialog()
        dialog.title("Add Customer")
        dialog.configure(bg=self.COLORS['background'])
        dialog.geometry("550x400")

        main_frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], 
                             highlightbackground=self.COLORS['border'], 
                             highlightthickness=2, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        tk.Label(main_frame, text="Add Customer", bg=self.COLORS['card_bg'], 
                 font=("Helvetica", 16, "bold"), fg=self.COLORS['primary_accent']).pack(anchor="center", pady=(0, 20))

        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        entry_style = {"font": ("Helvetica", 12)}
        dropdown_style = {"font": ("Helvetica", 12), "width": 30}

        user_name_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        user_name_frame.pack(fill="x", pady=(0, 15))
        tk.Label(user_name_frame, text="User Name:", **label_style, width=15, anchor="w").pack(side="left")
        user_name_var = tk.StringVar()
        user_name_entry = tk.Entry(user_name_frame, textvariable=user_name_var, **entry_style, width=30)
        user_name_entry.pack(side="left")
        user_name_entry.bind("<KeyRelease>", lambda e: self._format_user_name(user_name_var, user_name_entry))

        name_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        name_frame.pack(fill="x", pady=(0, 15))
        tk.Label(name_frame, text="Customer Name:", **label_style, width=15, anchor="w").pack(side="left")
        name_var = tk.StringVar()
        name_entry = tk.Entry(name_frame, textvariable=name_var, **entry_style, width=30)
        name_entry.pack(side="left")
        name_entry.bind("<KeyRelease>", lambda e: self._format_name(name_var, name_entry))

        phone_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        phone_frame.pack(fill="x", pady=(0, 15))
        tk.Label(phone_frame, text="Phone Number:", **label_style, width=15, anchor="w").pack(side="left")
        phone_var = tk.StringVar()
        phone_entry = tk.Entry(phone_frame, textvariable=phone_var, **entry_style, width=30)
        phone_entry.pack(side="left")

        package_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        package_frame.pack(fill="x", pady=(0, 15))
        tk.Label(package_frame, text="Select Package:", **label_style, width=15, anchor="w").pack(side="left")
        package_var = tk.StringVar()
        package_dropdown = ttk.Combobox(package_frame, textvariable=package_var, values=list(self.packages.keys()),
                                       state="readonly", **dropdown_style)
        package_dropdown.pack(side="left")

        region_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        region_frame.pack(fill="x", pady=(0, 30))
        tk.Label(region_frame, text="Select Region:", **label_style, width=15, anchor="w").pack(side="left")
        region_var = tk.StringVar()
        region_dropdown = ttk.Combobox(region_frame, textvariable=region_var, values=self.regions,
                                      state="readonly", **dropdown_style)
        region_dropdown.pack(side="left")

        def submit():
            try:
                user_name = self._validate_user_name(user_name_var.get().strip())
                name = self._validate_full_name(name_var.get().strip())
                phone = phone_var.get().strip() or None
                package_name = package_var.get()
                region = region_var.get()

                if phone and not self._validate_phone(phone):
                    raise ValueError("Phone number must be 11 digits starting with '03', e.g., 03123456789, or left empty")
                if not package_name:
                    raise ValueError("Please select a package")
                if not region:
                    raise ValueError("Please select a region")

                with sqlite3.connect(self.db_path, timeout=10) as conn:
                    c = conn.cursor()

                    c.execute("SELECT id FROM customers WHERE user_name = ? OR (phone = ? AND phone IS NOT NULL)", (user_name, phone))
                    if c.fetchone():
                        raise ValueError("Customer with this user name or phone number already exists")

                    package_id = self.packages[package_name][0]
                    create_date = datetime.now().strftime('%Y-%m-%d')

                    c.execute('''INSERT INTO customers (user_name, name, phone, package_id, create_date, status, region)
                                 VALUES (?, ?, ?, ?, ?, ?, ?)''',
                              (user_name, name, phone, package_id, create_date, 1, region))
                    c.execute("SELECT last_insert_rowid()")
                    customer_id = c.fetchone()[0]

                    package_price = self.packages[package_name][1]
                    c.execute('''INSERT INTO billing (customer_id, month, year, amount, is_paid)
                                 VALUES (?, ?, ?, ?, ?)''',
                              (customer_id, self.current_month, self.current_year, package_price, 0))

                messagebox.showinfo("Success", f"Customer {name} added successfully with package {package_name}.")
                dialog.destroy()
                
                self._load_customers()
                self._load_packages()
                
                for item in self.tree.get_children():
                    if self.tree.item(item)['values'][1] == user_name:
                        self.tree.selection_set(item)
                        self.tree.see(item)
                        break
        
            except ValueError as ve:
                messagebox.showerror("Error", str(ve))
            except Exception as e:
                messagebox.showerror("Error", f"Failed to add customer: {str(e)}")

        tk.Button(main_frame, text="Add Customer", command=submit, bg=self.COLORS['primary_accent'],
                  fg="#FFFFFF", font=("Helvetica", 12, "bold"), relief=tk.FLAT, padx=20, pady=10,
                  activebackground=self.COLORS['secondary_accent'], activeforeground="#FFFFFF").pack(anchor="center")

    def _edit_customer(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a customer to edit")
            return

        customer_id = self.tree.item(selected[0])['values'][0]
        customer_user_name = self.tree.item(selected[0])['values'][1]
        customer_name = self.tree.item(selected[0])['values'][2]
        customer_phone = self.tree.item(selected[0])['values'][3] or ""
        customer_package = self.tree.item(selected[0])['values'][4] or ""
        customer_region = self.tree.item(selected[0])['values'][5]

        dialog = self._create_dialog()
        dialog.title("Edit Customer")
        dialog.configure(bg=self.COLORS['background'])
        dialog.geometry("550x400")

        main_frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], 
                             highlightbackground=self.COLORS['border'], 
                             highlightthickness=2, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        tk.Label(main_frame, text="Edit Customer", bg=self.COLORS['card_bg'], 
                 font=("Helvetica", 16, "bold"), fg=self.COLORS['primary_accent']).pack(anchor="center", pady=(0, 20))

        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        entry_style = {"font": ("Helvetica", 12)}
        dropdown_style = {"font": ("Helvetica", 12), "width": 30}

        user_name_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        user_name_frame.pack(fill="x", pady=(0, 15))
        tk.Label(user_name_frame, text="User Name:", **label_style, width=15, anchor="w").pack(side="left")
        user_name_var = tk.StringVar(value=customer_user_name)
        user_name_entry = tk.Entry(user_name_frame, textvariable=user_name_var, **entry_style, width=30)
        user_name_entry.pack(side="left")
        user_name_entry.bind("<KeyRelease>", lambda e: self._format_user_name(user_name_var, user_name_entry))

        name_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        name_frame.pack(fill="x", pady=(0, 15))
        tk.Label(name_frame, text="Customer Name:", **label_style, width=15, anchor="w").pack(side="left")
        name_var = tk.StringVar(value=customer_name)
        name_entry = tk.Entry(name_frame, textvariable=name_var, **entry_style, width=30)
        name_entry.pack(side="left")
        name_entry.bind("<KeyRelease>", lambda e: self._format_name(name_var, name_entry))

        phone_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        phone_frame.pack(fill="x", pady=(0, 15))
        tk.Label(phone_frame, text="Phone Number:", **label_style, width=15, anchor="w").pack(side="left")
        phone_var = tk.StringVar(value=customer_phone)
        phone_entry = tk.Entry(phone_frame, textvariable=phone_var, **entry_style, width=30)
        phone_entry.pack(side="left")

        package_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        package_frame.pack(fill="x", pady=(0, 15))
        tk.Label(package_frame, text="Select Package:", **label_style, width=15, anchor="w").pack(side="left")
        package_var = tk.StringVar(value=customer_package)
        package_dropdown = ttk.Combobox(package_frame, textvariable=package_var, values=list(self.packages.keys()),
                                       **dropdown_style)
        package_dropdown.configure(state="readonly" if customer_package in ["", "None", None] else "disabled")
        package_dropdown.pack(side="left")

        region_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        region_frame.pack(fill="x", pady=(0, 30))
        tk.Label(region_frame, text="Select Region:", **label_style, width=15, anchor="w").pack(side="left")
        region_var = tk.StringVar(value=customer_region)
        region_dropdown = ttk.Combobox(region_frame, textvariable=region_var, values=self.regions,
                                      state="readonly", **dropdown_style)
        region_dropdown.pack(side="left")

        def submit():
            try:
                user_name = self._validate_user_name(user_name_var.get().strip())
                name = self._validate_full_name(name_var.get().strip())
                phone = phone_var.get().strip() or None
                package_name = package_var.get() if customer_package in ["", "None", None] else customer_package
                region = region_var.get()

                if phone and not self._validate_phone(phone):
                    raise ValueError("Phone number must be 11 digits starting with '03', e.g., 03123456789, or left empty")
                if not package_name:
                    raise ValueError("Please select a package")
                if not region:
                    raise ValueError("Please select a region")

                with sqlite3.connect(self.db_path, timeout=10) as conn:
                    c = conn.cursor()

                    c.execute("SELECT id FROM customers WHERE (user_name = ? OR (phone = ? AND phone IS NOT NULL)) AND id != ?", (user_name, phone, customer_id))
                    if c.fetchone():
                        raise ValueError("Customer with this user name or phone number already exists")

                    package_id = self.packages[package_name][0]
                    c.execute('''UPDATE customers 
                                 SET user_name = ?, name = ?, phone = ?, package_id = ?, region = ?
                                 WHERE id = ?''',
                              (user_name, name, phone, package_id, region, customer_id))

                    if customer_package in ["", "None", None] and package_name:
                        package_price = self.packages[package_name][1]
                        c.execute("UPDATE customers SET package_change_date = ? WHERE id = ?",
                                  (datetime.now().strftime('%Y-%m-%d'), customer_id))
                        c.execute('''INSERT OR IGNORE INTO billing (customer_id, month, year, amount, is_paid)
                                     VALUES (?, ?, ?, ?, ?)''',
                                  (customer_id, self.current_month, self.current_year, package_price, 0))

                    conn.commit()

                messagebox.showinfo("Success", f"Customer {name} updated successfully." + 
                                    (f" New bill of {self.packages[package_name][1]:.2f} PKR added for {self.current_month:02d}/{self.current_year}." if customer_package in ["", "None", None] and package_name else ""))
                dialog.destroy()
                self._load_customers()
                self.nav['refresh_billing']()
            except ValueError as ve:
                messagebox.showerror("Error", str(ve))
            except Exception as e:
                messagebox.showerror("Error", f"Failed to update customer: {str(e)}")

        tk.Button(main_frame, text="Update Customer", command=submit, bg=self.COLORS['primary_accent'],
                  fg="#FFFFFF", font=("Helvetica", 12, "bold"), relief=tk.FLAT, padx=20, pady=10,
                  activebackground=self.COLORS['secondary_accent'], activeforeground="#FFFFFF").pack(anchor="center")

    def _show_billing(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a customer to view billing")
            return

        customer_id = self.tree.item(selected[0])['values'][0]
        self.nav['show_billing'](customer_id)

    def refresh_customers(self):
        self._load_customers()
        
        # Check for month end carry forward
        # self._check_and_handle_month_end_carry_forward()  # <-- Remove or comment out this line

    def _import_customers(self):
        try:
            import_path = filedialog.askopenfilename(
                filetypes=[("Excel files", "*.xlsx")],
                title="Select Excel File to Import"
            )
            if not import_path:
                return

            # Show confirmation dialog for data wipe
            confirm = messagebox.askyesno(
                "Confirm Import",
                "WARNING: This will completely wipe out all existing customer data including:\n"
                "• All customers\n"
                "• All billing records\n"
                "• All payment history\n"
                "• All customer purchases\n\n"
                "This action cannot be undone. Do you want to continue?",
                icon='warning'
            )
            if not confirm:
                return

            df = pd.read_excel(import_path)

            # --- Add column validation ---
            required_columns = ['user_name', 'name', 'package', 'status', 'outstanding', 'credit']
            missing_cols = [col for col in required_columns if col not in df.columns]
            if missing_cols:
                messagebox.showerror("Error", f"Missing required columns in Excel file: {', '.join(missing_cols)}")
                return

            # Convert all columns to string type to avoid None issues
            df = df.astype(str)

            # Show progress dialog
            progress_window = self._create_progress_dialog(len(df))

            try:
                # Import with validation and progress tracking
                import_result = self.import_customers_with_validation(df, progress_window)

                # Close progress dialog
                progress_window.destroy()

                # Show import summary
                self._show_import_summary(import_result)

                # Refresh views if import was successful
                if import_result['successful_imports'] > 0:
                    self._load_customers()
                    if 'refresh_billing' in self.nav:
                        self.nav['refresh_billing']()
                    self._handle_carry_forward_unpaid_bills()

            except Exception as e:
                progress_window.destroy()
                raise e

        except Exception as e:
            messagebox.showerror("Error", f"Failed to import customers: {str(e)}")

    def import_customers_with_validation(self, df, progress_window):
        """Import customers with comprehensive validation and progress tracking"""
        import_result = {
            'total_rows': len(df),
            'successful_imports': 0,
            'failed_imports': 0,
            'failed_rows': [],
            'imported_ids': []
        }

        # Only use the expected columns, ignore any extras
        expected_columns = ['id', 'user_name', 'name', 'phone', 'package', 'region', 'status', 'outstanding', 'credit']
        df = df[[col for col in expected_columns if col in df.columns]]

        try:
            with sqlite3.connect(self.db_path, timeout=30) as conn:
                c = conn.cursor()
                c.execute("DELETE FROM billing")
                c.execute("DELETE FROM customers")
                c.execute("DELETE FROM payment_history")
                c.execute("DELETE FROM customer_purchases")
                # --- Add these lines at the start of the DB transaction ---
                # Get package and region data
                c.execute("SELECT id, name, price FROM packages")
                package_data = c.fetchall()
                package_map = {name.lower(): (id, price) for id, name, price in package_data}

                c.execute("SELECT name FROM regions")
                valid_regions = {row[0].lower() for row in c.fetchall()}

                import_date = datetime.now().strftime('%Y-%m-%d')
                # Get current max invoice number
                c.execute("SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER)) FROM billing WHERE invoice_number IS NOT NULL")
                max_inv_num = c.fetchone()[0] or 0
                next_inv_num = max_inv_num + 1

                for idx, row in df.iterrows():
                    try:
                        # --- Validate required fields exist ---
                        for col in ['user_name', 'name', 'package', 'status']:
                            if not pd.notna(row[col]) or not str(row[col]).strip():
                                raise ValueError(f"Required field '{col}' is missing at row {idx+2}")

                        # --- Handle package lookup safely ---
                        package_name = str(row['package']).strip() if pd.notna(row['package']) else None
                        if not package_name:
                            raise ValueError(f"Package name is required at row {idx+2}")
                        package_data = package_map.get(package_name.lower())
                        if not package_data:
                            raise ValueError(f"Package '{package_name}' not found in database at row {idx+2}")
                        package_id, package_price = package_data

                        # --- Ensure financial data is numeric ---
                        try:
                            outstanding = float(row['outstanding']) if pd.notna(row['outstanding']) else 0.0
                            credit = float(row['credit']) if pd.notna(row['credit']) else 0.0
                        except ValueError:
                            raise ValueError(f"Outstanding and credit must be numeric values at row {idx+2}")

                        # --- Additional validation ---
                        if outstanding < 0:
                            raise ValueError(f"Outstanding amount cannot be negative at row {idx+2}")
                        if credit < 0:
                            raise ValueError(f"Credit amount cannot be negative at row {idx+2}")
                        if outstanding > 0 and credit > 0:
                            raise ValueError(f"Customer cannot have both outstanding and credit amounts greater than zero at row {idx+2}")

                        # Insert customer
                        user_name = str(row['user_name']).strip()
                        name = str(row['name']).strip()
                        phone = str(row['phone']).strip() if pd.notna(row['phone']) else None
                        region = str(row['region']).strip() if pd.notna(row['region']) else None
                        status = 1 if str(row['status']).strip().lower() == 'active' else 0
                        c.execute('''INSERT INTO customers 
                            (id, user_name, name, phone, package_id, create_date, 
                              status, region, outstanding_amount, credit_balance)
                             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                  (row['id'], user_name, name, phone, package_id, import_date,
                                   status, region, outstanding, credit))
                        customer_id = row['id'] if 'id' in row and pd.notna(row['id']) else c.lastrowid
                        import_result['imported_ids'].append(customer_id)

                        # --- Billing logic (as before) ---
                        if credit > 0 and outstanding == 0:
                            if credit < package_price:
                                try:
                                    # Generate invoice number
                                    invoice_number = f"INV-{str(next_inv_num).zfill(4)}"
                                    next_inv_num += 1
                                    payment_date = datetime.now().strftime('%Y-%m-%d')  # Ensure payment_date is defined
                                    # Create billing record showing partial payment
                                    c.execute('''INSERT INTO billing
                                        (customer_id, month, year, amount, paid_amount, status,
                                         paid_date, invoice_number, paid_by, outstanding_amount, credit_amount)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                        (customer_id, self.current_month, self.current_year,
                                         package_price,          # Full amount
                                         credit,                # Amount paid from credit
                                         'Partially Paid',      # Status
                                         payment_date,          # Now properly defined
                                         invoice_number,
                                         "Excel Import",
                                         package_price - credit, # Remaining outstanding
                                         0                      # Credit fully used
                                        ))
                                    print(f"[DEBUG] Inserted billing: customer_id={customer_id}, amount={package_price}, paid_amount={credit}, status=Partially Paid, invoice={invoice_number}")
                                    # Update customer's credit balance to 0
                                    c.execute('''UPDATE customers SET credit_balance = 0 WHERE id = ?''', 
                                             (customer_id,))
                                    # Create payment history record
                                    c.execute('''INSERT INTO payment_history
                                        (invoice_number, customer_id, payment_date, amount_paid,
                                         credit_amount, outstanding_amount, paid_by)
                                        VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                        (invoice_number, customer_id, payment_date,
                                         credit,         # Amount paid
                                         credit,         # Credit used
                                         package_price - credit,  # Remaining outstanding
                                         "Excel Import"
                                        ))
                                    print(f"[DEBUG] Inserted payment_history: invoice={invoice_number}, amount_paid={credit}, outstanding={package_price - credit}")
                                    self._update_progress(progress_window, idx + 1, f"Imported row {idx+2} (credit applied as partial payment, bill and history created)")
                                except Exception as e:
                                    logging.error(f"Error inserting partial credit billing/payment for row {idx+2}: {e}")
                                    import_result['failed_imports'] += 1
                                    import_result['failed_rows'].append({
                                        'row': idx + 2,
                                        'user_name': str(row.get('user_name', '')),
                                        'errors': [str(e)]
                                    })
                                    continue
                            else:
                                remaining_credit = credit
                                future_month = self.current_month
                                future_year = self.current_year
                                last_bill_id = None
                                months_paid = 0
                                while remaining_credit >= package_price:
                                    invoice_number = f"INV-{str(next_inv_num).zfill(4)}"
                                    c.execute('''INSERT INTO billing
                                        (customer_id, month, year, amount, paid_amount, status, paid_date, invoice_number, paid_by, outstanding_amount, credit_amount)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                        (customer_id, future_month, future_year, package_price, package_price, 'Paid', import_date, invoice_number, "Excel Import", 0, 0))
                                    last_bill_id = c.lastrowid
                                    remaining_credit -= package_price
                                    months_paid += 1
                                    next_inv_num += 1
                                    future_month += 1
                                    if future_month > 12:
                                        future_month = 1
                                        future_year += 1
                                if remaining_credit > 0 and last_bill_id is not None:
                                    c.execute('''UPDATE billing SET credit_amount = ? WHERE id = ?''', (remaining_credit, last_bill_id))
                                c.execute('''UPDATE customers SET credit_balance = 0, outstanding_amount = 0 WHERE id = ?''', (customer_id,))
                                total_paid = months_paid * package_price
                                c.execute('''INSERT INTO payment_history (invoice_number, customer_id, payment_date, amount_paid, credit_amount, outstanding_amount, paid_by)
                                              VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                            (f"INV-{str(next_inv_num-1).zfill(4)}", customer_id, import_date, total_paid, remaining_credit, 0, "Excel Import"))
                                self._update_progress(progress_window, idx + 1, f"Imported row {idx+2}")
                        elif outstanding > 0 and credit == 0:
                            invoice_number = f"INV-{str(next_inv_num).zfill(4)}"
                            bill_amount = outstanding + (package_price if status == 1 and package_id else 0)
                            c.execute('''INSERT INTO billing
                                (customer_id, month, year, amount, paid_amount, status, paid_date, invoice_number, paid_by, outstanding_amount, credit_amount)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                (customer_id, self.current_month, self.current_year, bill_amount, 0, 'Unpaid', import_date, invoice_number, "Excel Import", outstanding, 0))
                            c.execute('''UPDATE customers SET credit_balance = 0, outstanding_amount = ? WHERE id = ?''', (outstanding, customer_id))
                            c.execute('''INSERT INTO payment_history (invoice_number, customer_id, payment_date, amount_paid, credit_amount, outstanding_amount, paid_by)
                                          VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                       (invoice_number, customer_id, import_date, 0, 0, outstanding, "Excel Import"))
                            next_inv_num += 1
                        elif credit == 0 and outstanding == 0:
                            invoice_number = f"INV-{str(next_inv_num).zfill(4)}"
                            c.execute('''INSERT INTO billing
                                (customer_id, month, year, amount, paid_amount, status, paid_date, invoice_number, paid_by, outstanding_amount, credit_amount)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                (customer_id, self.current_month, self.current_year, package_price, 0, 'Unpaid', import_date, invoice_number, "Excel Import", 0, 0))
                            c.execute('''UPDATE customers SET credit_balance = 0, outstanding_amount = 0 WHERE id = ?''', (customer_id,))
                            c.execute('''INSERT INTO payment_history (invoice_number, customer_id, payment_date, amount_paid, credit_amount, outstanding_amount, paid_by)
                                          VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                       (invoice_number, customer_id, import_date, 0, 0, 0, "Excel Import"))
                            next_inv_num += 1
                        import_result['successful_imports'] += 1
                        self._update_progress(progress_window, idx + 1, f"Imported row {idx+2}")
                    except Exception as e:
                        import_result['failed_imports'] += 1
                        import_result['failed_rows'].append({
                            'row': idx + 2,  # +2 because Excel is 1-based and we skip header
                            'user_name': str(row.get('user_name', '')),
                            'errors': [str(e)]
                        })
                        self._update_progress(progress_window, idx + 1, f"Failed row {idx+2}: {str(e)}")
                        continue

                conn.commit()
                return import_result

        except Exception as e:
            if 'conn' in locals():
                conn.rollback()
            logging.error(f"Import transaction failed: {str(e)}")
            raise e

    def _synchronize_financial_data_after_import(self):
        """Synchronize financial data after import to ensure consistency between customer table and billing records"""
        try:
            with sqlite3.connect(self.db_path, timeout=10) as conn:
                c = conn.cursor()

                # Get all customers
                c.execute("SELECT id FROM customers")
                customer_ids = [row[0] for row in c.fetchall()]

                for customer_id in customer_ids:
                    # Get customer's stored values
                    c.execute("SELECT outstanding_amount, credit_balance FROM customers WHERE id = ?", (customer_id,))
                    customer_data = c.fetchone()
                    if not customer_data:
                        continue

                    stored_outstanding, stored_credit = customer_data
                    stored_outstanding = stored_outstanding or 0.0
                    stored_credit = stored_credit or 0.0

                    # Calculate outstanding from billing records
                    c.execute('''
                        SELECT COALESCE(SUM(amount - COALESCE(paid_amount, 0)), 0) as total_outstanding
                        FROM billing
                        WHERE customer_id = ? AND (amount - COALESCE(paid_amount, 0)) > 0
                    ''', (customer_id,))
                    calculated_outstanding = c.fetchone()[0] or 0.0

                    # Get credit from last paid bill
                    c.execute('''
                        SELECT credit_amount FROM billing
                        WHERE customer_id = ? AND status = 'Paid' AND credit_amount > 0
                        ORDER BY year DESC, month DESC, id DESC
                        LIMIT 1
                    ''', (customer_id,))
                    last_bill_credit = c.fetchone()
                    bill_credit = last_bill_credit[0] if last_bill_credit and last_bill_credit[0] is not None else 0.0

                    # Use the most accurate values (prefer calculated for outstanding, max for credit)
                    final_outstanding = max(stored_outstanding, calculated_outstanding)
                    final_credit = max(stored_credit, bill_credit)

                    # Update customer table if values differ
                    if final_outstanding != stored_outstanding or final_credit != stored_credit:
                        c.execute('''
                            UPDATE customers
                            SET outstanding_amount = ?, credit_balance = ?
                            WHERE id = ?
                        ''', (final_outstanding, final_credit, customer_id))

                conn.commit()

        except Exception as e:
            logging.error(f"Error synchronizing financial data after import: {str(e)}")

    def _create_progress_dialog(self, total_rows):
        """Create a progress dialog for import operations"""
        progress_window = tk.Toplevel(self)
        progress_window.title("Importing Customers")
        progress_window.geometry("400x150")
        progress_window.transient(self)
        progress_window.grab_set()
        progress_window.resizable(False, False)

        # Center the dialog
        progress_window.update_idletasks()
        width = progress_window.winfo_width()
        height = progress_window.winfo_height()
        x = (progress_window.winfo_screenwidth() // 2) - (width // 2)
        y = (progress_window.winfo_screenheight() // 2) - (height // 2)
        progress_window.geometry(f"{width}x{height}+{x}+{y}")

        # Progress frame
        frame = tk.Frame(progress_window, bg=self.COLORS['card_bg'], padx=20, pady=20)
        frame.pack(fill="both", expand=True)

        # Status label
        status_label = tk.Label(frame, text="Preparing import...",
                               font=("Helvetica", 12), bg=self.COLORS['card_bg'])
        status_label.pack(pady=(0, 10))

        # Progress bar
        progress_bar = ttk.Progressbar(frame, length=300, mode='determinate')
        progress_bar.pack(pady=(0, 10))
        progress_bar['maximum'] = total_rows

        # Progress text
        progress_text = tk.Label(frame, text="0 / 0 rows processed",
                                font=("Helvetica", 10), bg=self.COLORS['card_bg'])
        progress_text.pack()

        # Store references for updating
        progress_window.status_label = status_label
        progress_window.progress_bar = progress_bar
        progress_window.progress_text = progress_text
        progress_window.total_rows = total_rows

        return progress_window

    def _update_progress(self, progress_window, current_row, status_text):
        """Update the progress dialog"""
        if progress_window and progress_window.winfo_exists():
            progress_window.status_label.config(text=status_text)
            progress_window.progress_bar['value'] = current_row
            progress_window.progress_text.config(text=f"{current_row} / {progress_window.total_rows} rows processed")
            progress_window.update()

    def _validate_customer_data(self, row, row_index):
        """Validate customer data before import"""
        errors = []

        try:
            # Basic field validation
            user_name = str(row['user_name']).strip() if pd.notna(row['user_name']) else ""
            name = str(row['name']).strip() if pd.notna(row['name']) else ""

            if not user_name:
                errors.append("User name is required")
            elif len(user_name) > 20:
                errors.append("User name must not exceed 20 characters")
            elif not re.match(r'^[a-zA-Z0-9.]+$', user_name):
                errors.append("User name must contain only letters, numbers, and dots")

            if not name:
                errors.append("Customer name is required")
            elif len(name) > 20:
                errors.append("Customer name must not exceed 20 characters")

            # Financial validation
            outstanding = float(row['outstanding']) if pd.notna(row['outstanding']) else 0.0
            credit = float(row['credit']) if pd.notna(row['credit']) else 0.0

            # Rule 1: No negative amounts
            if outstanding < 0:
                errors.append("Outstanding amount cannot be negative")
            if credit < 0:
                errors.append("Credit amount cannot be negative")

            # Rule 2: Credit and outstanding cannot both be positive
            if outstanding > 0 and credit > 0:
                errors.append("Customer cannot have both outstanding and credit amounts greater than zero")

            # Phone validation
            phone = str(row['phone']).strip() if pd.notna(row['phone']) else ""
            if phone and not re.match(r'^03\d{9}$', phone):
                errors.append("Phone number must be in format 03XXXXXXXXX")

            # Status validation
            status = str(row['status']).strip().lower() if pd.notna(row['status']) else ""
            if status not in ['active', 'inactive']:
                errors.append("Status must be 'Active' or 'Inactive'")

        except (ValueError, TypeError) as e:
            errors.append(f"Data type error: {str(e)}")
        except Exception as e:
            errors.append(f"Validation error: {str(e)}")

        return errors

    def _show_import_summary(self, import_result):
        """Show import summary dialog with detailed results"""
        summary_window = self._create_dialog()
        summary_window.title("Import Summary")
        summary_window.configure(bg=self.COLORS['background'])
        summary_window.geometry("600x500")

        # Center the dialog
        summary_window.update_idletasks()
        width = summary_window.winfo_width()
        height = summary_window.winfo_height()
        x = (summary_window.winfo_screenwidth() // 2) - (width // 2)
        y = (summary_window.winfo_screenheight() // 2) - (height // 2)
        summary_window.geometry(f"{width}x{height}+{x}+{y}")

        # Main frame with border styling to match add customer dialog
        main_frame = tk.Frame(summary_window, bg=self.COLORS['card_bg'],
                             highlightbackground=self.COLORS['border'],
                             highlightthickness=2, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        # Title with primary accent color to match add customer dialog
        title_label = tk.Label(main_frame, text="Customer Import Summary",
                              font=("Helvetica", 16, "bold"), bg=self.COLORS['card_bg'],
                              fg=self.COLORS['primary_accent'])
        title_label.pack(anchor="center", pady=(0, 20))

        # Summary stats with consistent styling
        stats_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        stats_frame.pack(fill="x", pady=(0, 20))

        # Use consistent font and text colors
        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 12), "fg": self.COLORS['text_primary']}

        tk.Label(stats_frame, text=f"Total Rows Processed: {import_result['total_rows']}",
                **label_style).pack(anchor="w", pady=2)
        tk.Label(stats_frame, text=f"Successful Imports: {import_result['successful_imports']}",
                bg=self.COLORS['card_bg'], font=("Helvetica", 12), fg="#4CAF50").pack(anchor="w", pady=2)
        tk.Label(stats_frame, text=f"Failed Imports: {import_result['failed_imports']}",
                bg=self.COLORS['card_bg'], font=("Helvetica", 12), fg="#F44336").pack(anchor="w", pady=2)

        # Failed rows details with consistent styling
        if import_result['failed_rows']:
            tk.Label(main_frame, text="Failed Rows Details:",
                    font=("Helvetica", 12, "bold"), bg=self.COLORS['card_bg'],
                    fg=self.COLORS['text_primary']).pack(anchor="w", pady=(10, 5))

            # Create scrollable text widget for errors with border styling
            text_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
            text_frame.pack(fill="both", expand=True, pady=(0, 20))

            scrollbar = ttk.Scrollbar(text_frame)
            scrollbar.pack(side="right", fill="y")

            error_text = tk.Text(text_frame, wrap=tk.WORD, yscrollcommand=scrollbar.set,
                                font=("Helvetica", 10), height=15, bg=self.COLORS['input_bg'],
                                fg=self.COLORS['text_primary'], relief=tk.FLAT,
                                highlightbackground=self.COLORS['border'], highlightthickness=1)
            error_text.pack(side="left", fill="both", expand=True)
            scrollbar.config(command=error_text.yview)

            # Add error details
            for failed_row in import_result['failed_rows']:
                row_num = failed_row['row']
                user_name = failed_row.get('user_name', 'Unknown')
                errors = failed_row['errors']

                error_text.insert(tk.END, f"Row {row_num} ({user_name}):\n")
                for error in errors:
                    error_text.insert(tk.END, f"  • {error}\n")
                error_text.insert(tk.END, "\n")

            error_text.config(state=tk.DISABLED)

        # Close button with styling to match add customer dialog
        tk.Button(main_frame, text="Close", command=summary_window.destroy,
                 bg=self.COLORS['primary_accent'], fg="#FFFFFF",
                 font=("Helvetica", 12, "bold"), relief=tk.FLAT, padx=20, pady=10,
                 activebackground=self.COLORS['secondary_accent'], activeforeground="#FFFFFF").pack(anchor="center", pady=10)

    def _handle_carry_forward_unpaid_bills(self):
        """Carry forward unpaid bills for each customer after import."""
        try:
            with sqlite3.connect(self.db_path, timeout=10) as conn:
                c = conn.cursor()
                # Get all customers
                c.execute("SELECT id FROM customers")
                customer_ids = [row[0] for row in c.fetchall()]
                for customer_id in customer_ids:
                    # Sum all unpaid bills from previous months (not current month/year)
                    c.execute('''SELECT COALESCE(SUM(amount - COALESCE(paid_amount, 0)), 0)
                                 FROM billing
                                 WHERE customer_id = ? AND status = 'Unpaid' AND (month != ? OR year != ?)''',
                              (customer_id, self.current_month, self.current_year))
                    carry_forward = c.fetchone()[0] or 0.0
                    # Update the customer's outstanding_amount if needed
                    if carry_forward > 0:
                        c.execute('''UPDATE customers SET outstanding_amount = outstanding_amount + ? WHERE id = ?''',
                                  (carry_forward, customer_id))
                conn.commit()
        except Exception as e:
            logging.error(f"Error in carry forward unpaid bills: {str(e)}")

    # --- Month End Credit Processing ---
    def process_month_end_credits(self):
        try:
            with sqlite3.connect(self.db_path, timeout=10) as conn:
                c = conn.cursor()
                c.execute('''SELECT c.id, c.credit_balance, b.id, b.amount, b.paid_amount, b.status
                             FROM customers c
                             JOIN billing b ON c.id = b.customer_id
                             WHERE c.credit_balance > 0 
                             AND b.status = 'Unpaid'
                             AND b.year = ? AND b.month = ?''',
                          (self.current_year, self.current_month))
                for customer_id, credit, bill_id, amount, paid_amount, status in c.fetchall():
                    payment_amount = min(credit, amount)
                    new_status = 'Partially Paid' if payment_amount < amount else 'Paid'
                    new_paid_amount = payment_amount
                    new_outstanding = max(0, amount - payment_amount)
                    invoice_number = f"INV-{str(self._generate_invoice_number())}"  # Use your invoice number logic
                    # Apply payment
                    c.execute('''UPDATE billing
                                 SET paid_amount = ?,
                                     status = ?,
                                     outstanding_amount = ?,
                                     invoice_number = ?
                                 WHERE id = ?''',
                              (new_paid_amount, new_status, new_outstanding, invoice_number, bill_id))
                    # Deduct from credit
                    c.execute('''UPDATE customers
                                 SET credit_balance = ?
                                 WHERE id = ?''',
                              (max(0, credit - payment_amount), customer_id))
                    # Record payment history
                    c.execute('''INSERT INTO payment_history (invoice_number, customer_id, payment_date, amount_paid, credit_amount, outstanding_amount, paid_by)
                                 VALUES (?, ?, date('now'), ?, ?, ?, ?)''',
                              (invoice_number, customer_id, payment_amount, payment_amount, new_outstanding, 'System Credit'))
                conn.commit()
        except Exception as e:
            import logging
            logging.error(f"Error processing month end credits: {str(e)}")

    def _create_dialog(self, *args, **kwargs):
        dialog = tk.Toplevel(self)
        dialog.transient(self.winfo_toplevel())
        dialog.grab_set()
        dialog.focus_set()
        dialog.protocol("WM_DELETE_WINDOW", lambda d=dialog: self._close_dialog(d))
        self._dialogs.append(dialog)
        return dialog

    def _close_dialog(self, dialog):
        if dialog in self._dialogs:
            self._dialogs.remove(dialog)
        dialog.destroy()

    def _on_main_minimize(self, event=None):
        for dialog in self._dialogs:
            try:
                dialog.iconify()
            except Exception:
                pass

    def _on_main_restore(self, event=None):
        for dialog in self._dialogs:
            try:
                dialog.deiconify()
                dialog.lift()
            except Exception:
                pass

class CustomerImporter:
    def __init__(self, db_path, current_month, current_year):
        self.db_path = db_path
        self.current_month = current_month
        self.current_year = current_year

    def import_customers(self, df, current_username="Excel Import"):
        """Main method to handle customer imports with all billing scenarios"""
        try:
            with sqlite3.connect(self.db_path, timeout=10) as conn:
                c = conn.cursor()
                conn.execute("BEGIN TRANSACTION")

                # Clear existing data
                c.execute("DELETE FROM billing")
                c.execute("DELETE FROM customers")
                c.execute("DELETE FROM payment_history")
                c.execute("DELETE FROM customer_purchases")

                # Get package and region data
                c.execute("SELECT id, name, price FROM packages")
                package_data = c.fetchall()
                package_map = {name.lower(): (id, price) for id, name, price in package_data}

                c.execute("SELECT name FROM regions")
                valid_regions = {row[0].lower() for row in c.fetchall()}

                import_date = datetime.now().strftime('%Y-%m-%d')
                imported_ids = []
                # Get current max invoice number
                c.execute("SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER)) FROM billing WHERE invoice_number IS NOT NULL")
                max_inv_num = c.fetchone()[0] or 0
                next_inv_num = max_inv_num + 1

                for _, row in df.iterrows():
                    try:
                        user_name = str(row['user_name']).strip()
                        name = str(row['name']).strip()
                        phone = str(row['phone']).strip() if pd.notna(row['phone']) else None
                        package_name = str(row['package']).strip() if pd.notna(row['package']) else None
                        region = str(row['region']).strip() if pd.notna(row['region']) else None
                        status = 1 if str(row['status']).strip().lower() == 'active' else 0
                        outstanding = float(row['outstanding']) if pd.notna(row['outstanding']) else 0.0
                        credit = float(row['credit']) if pd.notna(row['credit']) else 0.0

                        if not user_name or not name or (package_name and package_name.lower() not in package_map):
                            continue  # skip invalid row

                        package_id, package_price = package_map[package_name.lower()] if package_name else (None, 0.0)
                        # Insert customer
                        c.execute('''INSERT INTO customers 
                            (id, user_name, name, phone, package_id, create_date, 
                              status, region, outstanding_amount, credit_balance)
                             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                  (row['id'], user_name, name, phone, package_id, import_date,
                                   status, region, outstanding, credit))
                        customer_id = row['id'] if 'id' in row and pd.notna(row['id']) else c.lastrowid
                        imported_ids.append(customer_id)

                        # ---- Handle billing import logic
                        if credit > 0 and outstanding == 0:
                            if credit < package_price:
                                # Only set the credit balance, do not create a bill or payment_history yet
                                c.execute('''UPDATE customers SET credit_balance = ? WHERE id = ?''', (credit, customer_id))
                                # No billing or payment_history insert here
                                print(f"[DEBUG] Inserted billing: customer_id={customer_id}, amount={package_price}, paid_amount={credit}, status=Partially Paid, invoice={invoice_number}")
                                # Update customer's credit balance to 0
                                c.execute('''UPDATE customers SET credit_balance = 0 WHERE id = ?''', 
                                         (customer_id,))
                                # Create payment history record
                                c.execute('''INSERT INTO payment_history
                                    (invoice_number, customer_id, payment_date, amount_paid,
                                     credit_amount, outstanding_amount, paid_by)
                                    VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                    (invoice_number, customer_id, payment_date,
                                     credit,         # Amount paid
                                     credit,         # Credit used
                                     package_price - credit,  # Remaining outstanding
                                     "Excel Import"
                                    ))
                                print(f"[DEBUG] Inserted payment_history: invoice={invoice_number}, amount_paid={credit}, outstanding={package_price - credit}")
                                self._update_progress(progress_window, idx + 1, f"Imported row {idx+2} (credit carried forward)")
                            else:
                                remaining_credit = credit
                                future_month = self.current_month
                                future_year = self.current_year
                                last_bill_id = None
                                months_paid = 0
                                while remaining_credit >= package_price:
                                    invoice_number = f"INV-{str(next_inv_num).zfill(4)}"
                                    c.execute('''INSERT INTO billing
                                        (customer_id, month, year, amount, paid_amount, status, paid_date, invoice_number, paid_by, outstanding_amount, credit_amount)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                        (customer_id, future_month, future_year, package_price, package_price, 'Paid', import_date, invoice_number, "Excel Import", 0, 0))
                                    last_bill_id = c.lastrowid
                                    remaining_credit -= package_price
                                    months_paid += 1
                                    next_inv_num += 1
                                    future_month += 1
                                    if future_month > 12:
                                        future_month = 1
                                        future_year += 1
                                if remaining_credit > 0 and last_bill_id is not None:
                                    c.execute('''UPDATE billing SET credit_amount = ? WHERE id = ?''', (remaining_credit, last_bill_id))
                                c.execute('''UPDATE customers SET credit_balance = 0, outstanding_amount = 0 WHERE id = ?''', (customer_id,))
                                total_paid = months_paid * package_price
                                c.execute('''INSERT INTO payment_history (invoice_number, customer_id, payment_date, amount_paid, credit_amount, outstanding_amount, paid_by)
                                              VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                            (f"INV-{str(next_inv_num-1).zfill(4)}", customer_id, import_date, total_paid, remaining_credit, 0, "Excel Import"))
                                self._update_progress(progress_window, idx + 1, f"Imported row {idx+2}")
                        elif outstanding > 0 and credit == 0:
                            invoice_number = f"INV-{str(next_inv_num).zfill(4)}"
                            bill_amount = outstanding + (package_price if status == 1 and package_id else 0)
                            c.execute('''INSERT INTO billing
                                (customer_id, month, year, amount, paid_amount, status, paid_date, invoice_number, paid_by, outstanding_amount, credit_amount)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                (customer_id, self.current_month, self.current_year, bill_amount, 0, 'Unpaid', import_date, invoice_number, "Excel Import", outstanding, 0))
                            c.execute('''UPDATE customers SET credit_balance = 0, outstanding_amount = ? WHERE id = ?''', (outstanding, customer_id))
                            c.execute('''INSERT INTO payment_history (invoice_number, customer_id, payment_date, amount_paid, credit_amount, outstanding_amount, paid_by)
                                          VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                       (invoice_number, customer_id, import_date, 0, 0, outstanding, "Excel Import"))
                            next_inv_num += 1
                        elif credit == 0 and outstanding == 0:
                            invoice_number = f"INV-{str(next_inv_num).zfill(4)}"
                            c.execute('''INSERT INTO billing
                                (customer_id, month, year, amount, paid_amount, status, paid_date, invoice_number, paid_by, outstanding_amount, credit_amount)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                (customer_id, self.current_month, self.current_year, package_price, 0, 'Unpaid', import_date, invoice_number, "Excel Import", 0, 0))
                            c.execute('''UPDATE customers SET credit_balance = 0, outstanding_amount = 0 WHERE id = ?''', (customer_id,))
                            c.execute('''INSERT INTO payment_history (invoice_number, customer_id, payment_date, amount_paid, credit_amount, outstanding_amount, paid_by)
                                          VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                       (invoice_number, customer_id, import_date, 0, 0, 0, "Excel Import"))
                            next_inv_num += 1
                    except Exception as e:
                        logging.error(f"Error processing row {_}: {str(e)}")
                        continue

                conn.commit()
                return imported_ids

        except Exception as e:
            if 'conn' in locals():
                conn.rollback()
            raise e

    def _handle_billing_scenarios(self, cursor, row, customer_id, package_map, import_date, current_username, next_inv_num):
        """Handle billing scenarios during import - simplified approach that preserves imported financial data"""
        status = 1 if str(row['status']).strip().lower() == 'active' else 0
        outstanding = float(row['outstanding']) if pd.notna(row['outstanding']) else 0.0
        credit = float(row['credit']) if pd.notna(row['credit']) else 0.0
        package_name = str(row['package']).strip() if pd.notna(row['package']) else None
        package_id = package_map[package_name.lower()][0] if package_name else None
        package_price = package_map[package_name.lower()][1] if package_name else 0.0
        
        invoice_number = f"INV-{str(next_inv_num).zfill(4)}"
        
        # Create a single billing record that matches the imported values
        if outstanding > 0:
            cursor.execute('''INSERT INTO billing
                (customer_id, month, year, amount, paid_amount, status, paid_date, invoice_number, paid_by, outstanding_amount, credit_amount)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                (customer_id, self.current_month, self.current_year, outstanding, 0, 'Unpaid', import_date, invoice_number, 'Excel Import', outstanding, 0))
        
        if credit > 0:
            invoice_number = f"INV-{str(next_inv_num + 1).zfill(4)}"
            cursor.execute('''INSERT INTO billing
                (customer_id, month, year, amount, paid_amount, status, paid_date, invoice_number, paid_by, outstanding_amount, credit_amount)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                (customer_id, self.current_month, self.current_year, credit, credit, 'Paid', import_date, invoice_number, 'Excel Import', 0, credit))
        
        # Ensure customer table has the correct values
        cursor.execute('''UPDATE customers SET outstanding_amount = ?, credit_balance = ? WHERE id = ?''', 
                      (outstanding, credit, customer_id))
        
        return next_inv_num + 2  # Return the next available invoice number

    def _process_customer_row(self, cursor, row, package_map, valid_regions):
        """Process a single customer row and return customer_id"""
        # Validate and extract data
        user_name = str(row['user_name']).strip()
        name = str(row['name']).strip()
        phone = str(row['phone']).strip() if pd.notna(row['phone']) else None
        package_name = str(row['package']).strip() if pd.notna(row['package']) else None
        region = str(row['region']).strip() if pd.notna(row['region']) else None
        status = 1 if str(row['status']).strip().lower() == 'active' else 0
        outstanding = float(row['outstanding']) if pd.notna(row['outstanding']) else 0.0
        credit = float(row['credit']) if pd.notna(row['credit']) else 0.0
        
        # Validate data
        if not user_name:
            raise ValueError("User name is required")
        if not name:
            raise ValueError("Name is required")
        if phone and not re.match(r'^03\d{9}$', phone):
            raise ValueError(f"Invalid phone format: {phone}")
        if package_name and package_name.lower() not in package_map:
            raise ValueError(f"Invalid package: {package_name}")
        if region and region.lower() not in valid_regions:
            raise ValueError(f"Invalid region: {region}")
        if credit > 0 and outstanding > 0:
            raise ValueError("Credit and outstanding amounts cannot both be greater than 0")
        
        # Get package info
        package_id = package_map[package_name.lower()][0] if package_name else None
        package_price = package_map[package_name.lower()][1] if package_name else 0.0
        
        # Insert customer
        cursor.execute('''INSERT INTO customers 
                         (id, user_name, name, phone, package_id, create_date, 
                          status, region, outstanding_amount, credit_balance)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                      (row['id'], user_name, name, phone, package_id, datetime.now().strftime('%Y-%m-%d'),
                       status, region, outstanding, credit))
        
        return row['id'] if 'id' in row and pd.notna(row['id']) else cursor.lastrowid
